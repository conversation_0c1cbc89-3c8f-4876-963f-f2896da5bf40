@import "tailwindcss";

@theme {
  --font-alibaba: "alibaba-sans", sans-serif;
}

body {
  background-color: #faf5f5;
}

/* Custom Select Styling */
.custom-select .ant-select-selector {
  border-radius: 8px !important;
  border: 1px solid #d1d5db !important;
  height: 48px !important;
  padding: 0 12px 0 40px !important;
  transition: all 0.2s ease !important;
}

.custom-select .ant-select-selector:hover {
  border-color: #f97316 !important;
}

.custom-select.ant-select-focused .ant-select-selector {
  border-color: #f97316 !important;
  box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.2) !important;
}

.custom-select .ant-select-selection-placeholder {
  color: #9ca3af !important;
  line-height: 46px !important;
}

.custom-select .ant-select-selection-item {
  line-height: 46px !important;
  color: #111827 !important;
}

/* Custom Checkbox Styling */
.ant-checkbox-wrapper {
  color: #6b7280 !important;
}

.ant-checkbox-checked .ant-checkbox-inner {
  background-color: #f97316 !important;
  border-color: #f97316 !important;
}

.ant-checkbox:hover .ant-checkbox-inner {
  border-color: #f97316 !important;
}

/* Form Item Styling */
.ant-form-item {
  margin-bottom: 0 !important;
}

.ant-form-item-explain-error {
  color: #ef4444 !important;
  font-size: 0.875rem !important;
  margin-top: 0.25rem !important;
}

/* Remove Ant Design default focus styles */
.ant-input:focus,
.ant-input-focused {
  border-color: transparent !important;
  box-shadow: none !important;
}

/* Custom scrollbar for select dropdown */
.ant-select-dropdown {
  border-radius: 8px !important;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
}

.ant-select-item-option-selected {
  background-color: #fed7aa !important;
  color: #ea580c !important;
}

.ant-select-item-option:hover {
  background-color: #ffedd5 !important;
}