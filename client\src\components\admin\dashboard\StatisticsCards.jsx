import React from 'react';
import { Card, Row, Col, Typography } from 'antd';
import {
  DollarOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  ShopOutlined,
  ShoppingOutlined,
  ClockCircleOutlined,
  WarningOutlined
} from '@ant-design/icons';

// Custom INR Icon component
const INRIcon = () => (
  <span style={{ fontSize: '20px', fontWeight: 'bold' }}>₹</span>
);

const { Text } = Typography;

const StatCard = ({ 
  title, 
  value, 
  icon, 
  color, 
  bgColor, 
  suffix = '', 
  precision = 0,
  subText = null,
  realTimeValue = null
}) => (
  <Card 
    style={{ 
      borderRadius: '12px', 
      border: 'none',
      boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
      overflow: 'hidden',
      height: '100%'
    }}
  >
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'space-between',
      height: '100%'
    }}>
      <div style={{ flex: 1 }}>
        <Text type="secondary" style={{ 
          fontSize: '12px', 
          textTransform: 'uppercase', 
          fontWeight: '500',
          display: 'block'
        }}>
          {title}
        </Text>
        <div style={{ margin: '8px 0 4px 0' }}>
          <Text style={{ 
            fontSize: 'clamp(20px, 4vw, 28px)', 
            fontWeight: 'bold', 
            color 
          }}>
            {precision > 0 
              ? typeof value === 'number' 
                ? value.toLocaleString('en-US', { minimumFractionDigits: precision })
                : '0.00'
              : typeof value === 'number' 
                ? value.toLocaleString()
                : value || '0'
            }
            {suffix && <span style={{ fontSize: '14px', marginLeft: '4px' }}>{suffix}</span>}
          </Text>
        </div>
        {subText && (
          <Text type="secondary" style={{ fontSize: '11px', display: 'block' }}>
            {subText}
          </Text>
        )}
        {realTimeValue && (
          <div style={{ marginTop: '8px' }}>
            <Text type="secondary" style={{ fontSize: '11px' }}>
              {realTimeValue}
            </Text>
          </div>
        )}
      </div>
      <div style={{
        width: '48px',
        height: '48px',
        borderRadius: '12px',
        background: bgColor,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        flexShrink: 0
      }}>
        {React.cloneElement(icon, { style: { fontSize: '24px', color } })}
      </div>
    </div>
  </Card>
);

const StatisticsCards = ({ dashboardData, realTimeData, vendorStats, productStats, pendingApprovals }) => {
  const mainStats = [
    {
      title: 'Total Revenue',
      value: dashboardData?.overview?.orders?.totalRevenue || 0,
      icon: <INRIcon />,
      color: '#52c41a',
      bgColor: 'rgba(82, 196, 26, 0.1)',
      suffix: '',
      precision: 2,
      realTimeValue: realTimeData?.today?.revenue 
        ? `Today: ₹${realTimeData.today.revenue.toFixed(2)}` 
        : null
    },
    {
      title: 'Total Orders',
      value: dashboardData?.overview?.orders?.totalOrders || 0,
      icon: <ShoppingCartOutlined />,
      color: '#1890ff',
      bgColor: 'rgba(24, 144, 255, 0.1)',
      realTimeValue: realTimeData?.today?.orders 
        ? `Today: ${realTimeData.today.orders}` 
        : null
    },
    {
      title: 'Total Users',
      value: dashboardData?.overview?.users?.totalUsers || 0,
      icon: <UserOutlined />,
      color: '#722ed1',
      bgColor: 'rgba(114, 46, 209, 0.1)',
      realTimeValue: realTimeData?.today?.activeUsers 
        ? `Active: ${realTimeData.today.activeUsers}` 
        : null
    },
    {
      title: 'Total Vendors',
      value: vendorStats?.totalVendors || dashboardData?.overview?.vendors?.totalVendors || 0,
      icon: <ShopOutlined />,
      color: '#fa8c16',
      bgColor: 'rgba(250, 140, 22, 0.1)',
      subText: `Active: ${vendorStats?.activeVendors || dashboardData?.overview?.vendors?.activeVendors || 0}`
    }
  ];

  const additionalStats = [
    {
      title: 'Total Products',
      value: productStats?.totalProducts || 0,
      icon: <ShoppingOutlined />,
      color: '#52c41a',
      bgColor: 'rgba(82, 196, 26, 0.1)',
      subText: `Active: ${productStats?.activeProducts || 0}`
    },
    {
      title: 'Pending Approvals',
      value: pendingApprovals?.length || 0,
      icon: <ClockCircleOutlined />,
      color: '#faad14',
      bgColor: 'rgba(250, 173, 20, 0.1)',
      subText: pendingApprovals 
        ? `Products: ${pendingApprovals.filter(p => p.type === 'product').length} | Vendors: ${pendingApprovals.filter(p => p.type === 'vendor').length}`
        : 'Products: 0 | Vendors: 0'
    },
    {
      title: 'Low Stock Items',
      value: productStats?.lowStockProducts || 0,
      icon: <WarningOutlined />,
      color: '#f5222d',
      bgColor: 'rgba(245, 34, 45, 0.1)',
      subText: 'Requires attention'
    },
    {
      title: 'Commission Pending',
      value: vendorStats?.totalPendingCommission || dashboardData?.commissions?.pending || 0,
      icon: <INRIcon />,
      color: '#1890ff',
      bgColor: 'rgba(24, 144, 255, 0.1)',
      suffix: '',
      precision: 2,
      subText: 'Awaiting payout'
    }
  ];

  return (
    <>
      {/* Main Statistics Row */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        {mainStats.map((stat, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <StatCard {...stat} />
          </Col>
        ))}
      </Row>

      {/* Additional Metrics Row */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        {additionalStats.map((stat, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <StatCard {...stat} />
          </Col>
        ))}
      </Row>
    </>
  );
};

export default StatisticsCards;
