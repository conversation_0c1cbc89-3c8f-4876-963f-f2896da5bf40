@echo off
echo Fixing Vite EPERM Error - Windows File Permission Issue
echo =====================================================

echo Step 1: Stopping all Node.js processes...
taskkill /f /im node.exe 2>nul
timeout /t 2 /nobreak >nul

echo Step 2: Stopping Vite development server...
taskkill /f /im "vite.exe" 2>nul
timeout /t 1 /nobreak >nul

echo Step 3: Removing problematic Vite cache directories...
if exist "node_modules\.vite" (
    echo Removing .vite cache...
    rmdir /s /q "node_modules\.vite" 2>nul
    timeout /t 1 /nobreak >nul
)

if exist "node_modules\.vite\deps_temp*" (
    echo Removing temporary deps...
    for /d %%i in ("node_modules\.vite\deps_temp*") do rmdir /s /q "%%i" 2>nul
)

echo Step 4: Clearing npm cache...
npm cache clean --force

echo Step 5: Removing node_modules and package-lock.json...
if exist "node_modules" (
    echo Removing node_modules...
    rmdir /s /q "node_modules"
)
if exist "package-lock.json" (
    echo Removing package-lock.json...
    del "package-lock.json"
)

echo Step 6: Fresh installation...
npm install

echo Step 7: Starting development server...
echo Starting in 3 seconds...
timeout /t 3 /nobreak >nul
npm run dev

echo Done! The EPERM error should be resolved.
pause