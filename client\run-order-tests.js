#!/usr/bin/env node

/**
 * Order Status Flow Test Runner
 * 
 * This script runs the comprehensive order status flow tests
 * and provides detailed reporting of the results.
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting Order Status Flow Tests...\n');

// Test configuration
const testConfig = {
  testFile: 'src/tests/orderStatusFlow.test.js',
  coverage: true,
  verbose: true
};

// Build test command
const testCommand = 'npm';
const testArgs = ['run', 'test:order-flow'];

if (testConfig.coverage) {
  testArgs.push('--', '--coverage');
}

if (testConfig.verbose) {
  testArgs.push('--reporter=verbose');
}

// Run tests
const testProcess = spawn(testCommand, testArgs, {
  stdio: 'inherit',
  shell: true,
  cwd: process.cwd()
});

testProcess.on('close', (code) => {
  console.log('\n' + '='.repeat(60));
  
  if (code === 0) {
    console.log('✅ All Order Status Flow Tests Passed!');
    console.log('\n📋 Next Steps:');
    console.log('1. Run manual tests using: client/src/tests/ORDER_STATUS_FLOW_MANUAL_TEST.md');
    console.log('2. Test in different browsers');
    console.log('3. Test with real data in staging environment');
    console.log('4. Verify socket connections work correctly');
  } else {
    console.log('❌ Some tests failed. Please check the output above.');
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Ensure all dependencies are installed: npm install');
    console.log('2. Check that mock data matches actual API responses');
    console.log('3. Verify component imports are correct');
    console.log('4. Run tests individually to isolate issues');
  }
  
  console.log('='.repeat(60));
  process.exit(code);
});

testProcess.on('error', (error) => {
  console.error('❌ Failed to start test process:', error.message);
  console.log('\n💡 Make sure you have installed dependencies:');
  console.log('npm install');
  process.exit(1);
});
