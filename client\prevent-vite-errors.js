#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 Setting up Vite EPERM Error Prevention...\n');

// Create vite.config.js with optimized settings for Windows
const viteConfigPath = path.join(__dirname, 'vite.config.js');

const viteConfig = `import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  
  // Optimize for Windows to prevent EPERM errors
  optimizeDeps: {
    // Force pre-bundling of these dependencies
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'antd',
      '@ant-design/icons',
      'axios',
      'dayjs'
    ],
    // Exclude problematic packages
    exclude: []
  },
  
  // Server configuration
  server: {
    // Use polling for file watching on Windows
    watch: {
      usePolling: true,
      interval: 1000
    },
    // Prevent EPERM errors
    fs: {
      strict: false
    }
  },
  
  // Build configuration
  build: {
    // Reduce chunk size to prevent memory issues
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          antd: ['antd', '@ant-design/icons'],
          router: ['react-router-dom']
        }
      }
    }
  },
  
  // Cache directory configuration
  cacheDir: '.vite-cache',
  
  // Resolve configuration
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  }
})
`;

try {
  fs.writeFileSync(viteConfigPath, viteConfig);
  console.log('✅ Created optimized vite.config.js');
} catch (error) {
  console.log('❌ Failed to create vite.config.js:', error.message);
}

// Create .gitignore entries for Vite cache
const gitignorePath = path.join(__dirname, '.gitignore');
let gitignoreContent = '';

if (fs.existsSync(gitignorePath)) {
  gitignoreContent = fs.readFileSync(gitignorePath, 'utf8');
}

const viteIgnoreEntries = `
# Vite cache directories
.vite-cache/
node_modules/.vite/
dist/

# Temporary files
*.tmp
*.temp
.DS_Store
Thumbs.db
`;

if (!gitignoreContent.includes('.vite-cache/')) {
  gitignoreContent += viteIgnoreEntries;
  
  try {
    fs.writeFileSync(gitignorePath, gitignoreContent);
    console.log('✅ Updated .gitignore with Vite cache entries');
  } catch (error) {
    console.log('❌ Failed to update .gitignore:', error.message);
  }
}

// Create package.json scripts for cache management
const packageJsonPath = path.join(__dirname, 'package.json');

if (fs.existsSync(packageJsonPath)) {
  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    if (!packageJson.scripts) {
      packageJson.scripts = {};
    }
    
    // Add cache management scripts
    packageJson.scripts = {
      ...packageJson.scripts,
      "clean": "rimraf node_modules/.vite .vite-cache dist",
      "clean:cache": "rimraf node_modules/.vite .vite-cache",
      "clean:deps": "rimraf node_modules package-lock.json && npm install",
      "dev:clean": "npm run clean:cache && npm run dev",
      "fix-eperm": "taskkill /f /im node.exe & rimraf node_modules/.vite & npm run dev"
    };
    
    // Add rimraf as dev dependency if not present
    if (!packageJson.devDependencies) {
      packageJson.devDependencies = {};
    }
    
    if (!packageJson.devDependencies.rimraf && !packageJson.dependencies?.rimraf) {
      packageJson.devDependencies.rimraf = "^5.0.0";
    }
    
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    console.log('✅ Added cache management scripts to package.json');
    
  } catch (error) {
    console.log('❌ Failed to update package.json:', error.message);
  }
}

// Create Windows-specific development script
const devScriptPath = path.join(__dirname, 'dev-windows.bat');
const devScript = `@echo off
echo Starting Vite development server (Windows optimized)...

REM Kill any existing Node processes
taskkill /f /im node.exe 2>nul

REM Clean Vite cache
if exist "node_modules\\.vite" rmdir /s /q "node_modules\\.vite"
if exist ".vite-cache" rmdir /s /q ".vite-cache"

REM Start development server
npm run dev

pause
`;

try {
  fs.writeFileSync(devScriptPath, devScript);
  console.log('✅ Created dev-windows.bat script');
} catch (error) {
  console.log('❌ Failed to create dev-windows.bat:', error.message);
}

console.log('\n🎯 EPERM Error Prevention Setup Complete!');
console.log('\n📋 Available commands:');
console.log('   npm run clean         - Clean all cache and build files');
console.log('   npm run clean:cache   - Clean only Vite cache');
console.log('   npm run clean:deps    - Clean and reinstall dependencies');
console.log('   npm run dev:clean     - Clean cache and start dev server');
console.log('   npm run fix-eperm     - Quick fix for EPERM errors');
console.log('   dev-windows.bat       - Windows-optimized dev server');

console.log('\n💡 Prevention Tips:');
console.log('   1. Always stop dev server properly (Ctrl+C)');
console.log('   2. Use "npm run dev:clean" if you encounter issues');
console.log('   3. Run "npm run clean:cache" before switching branches');
console.log('   4. Use dev-windows.bat for Windows-specific optimizations');

console.log('\n🚀 Next steps:');
console.log('   1. Run: npm install (to install rimraf)');
console.log('   2. Use: npm run dev:clean (instead of npm run dev)');
console.log('   3. If EPERM occurs: run fix-vite-eperm-error.bat');