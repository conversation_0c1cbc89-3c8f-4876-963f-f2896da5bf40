import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { notification } from 'antd';
import { useCart } from '../contexts/CartContext';
import { useAuth } from '../hooks/useAuth';
import { getAbsoluteImageUrl, getProductImageUrls, getPrimaryProductImage } from '../utils/imageUtils';

// Simple SVG Icons as components
const ChevronLeftIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
  </svg>
);

const ChevronRightIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
  </svg>
);

const MagnifyingGlassIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
  </svg>
);

const ShoppingCartIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293A1 1 0 004 16h16M7 13v4a2 2 0 002 2h6a2 2 0 002-2v-4m-8 2h4" />
  </svg>
);

const ShareIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
  </svg>
);

const StarSolidIcon = ({ className }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clipRule="evenodd" />
  </svg>
);

const TruckIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
  </svg>
);

const ShieldCheckIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
  </svg>
);

const ArrowPathIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
  </svg>
);

const ProductInfoCard = ({ product }) => {
  const [selectedImage, setSelectedImage] = useState(0);
  const [selectedVariant, setSelectedVariant] = useState(null);
  const [quantity, setQuantity] = useState(1);
  const [activeTab, setActiveTab] = useState('description');

  // Sample product data (replace with props)
  const defaultProduct = {
    id: 1,
    name: "Premium Wireless Bluetooth Headphones",
    brand: "TechSound",
    sku: "TS-WH-001",
    price: 199.99,
    originalPrice: 299.99,
    discount: 33,
    rating: 4.5,
    reviewCount: 1247,
    inStock: true,
    stockCount: 15,
    images: [
      "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=600&h=600&auto=format&fit=crop",
      "https://images.unsplash.com/photo-1484704849700-f032a568e944?w=600&h=600&auto=format&fit=crop",
      "https://images.unsplash.com/photo-1487215078519-e21cc028cb29?w=600&h=600&auto=format&fit=crop",
      "https://images.unsplash.com/photo-1583394838336-acd977736f90?w=600&h=600&auto=format&fit=crop"
    ],
    variants: [
      { id: 1, name: "Black", color: "#000000", available: true },
      { id: 2, name: "White", color: "#FFFFFF", available: true },
      { id: 3, name: "Blue", color: "#3B82F6", available: false }
    ],
    badges: ["Best Seller", "Free Shipping"],
    description: "Experience premium sound quality with our latest wireless Bluetooth headphones. Featuring advanced noise cancellation, 30-hour battery life, and premium comfort padding for all-day wear.",
    specifications: {
      "Audio": {
        "Driver Size": "40mm",
        "Frequency Response": "20Hz - 20kHz",
        "Impedance": "32 Ohms",
        "Sensitivity": "105dB"
      },
      "Connectivity": {
        "Bluetooth Version": "5.2",
        "Range": "10 meters",
        "Codecs": "SBC, AAC, aptX"
      },
      "Battery": {
        "Battery Life": "30 hours",
        "Charging Time": "2 hours",
        "Quick Charge": "15 min = 3 hours"
      },
      "Physical": {
        "Weight": "250g",
        "Dimensions": "190 x 160 x 80mm",
        "Foldable": "Yes"
      }
    },
    features: [
      "Active Noise Cancellation",
      "Touch Controls",
      "Voice Assistant Compatible",
      "Foldable Design",
      "Premium Materials"
    ],
    shipping: {
      freeShipping: true,
      estimatedDays: "2-3",
      returnPolicy: "30-day return"
    }
  };

  const productData = product || defaultProduct;
  
  // Process images using our utility functions
  const processedImages = productData.images && productData.images.length > 0 
    ? productData.images.map(img => {
        if (typeof img === 'string') {
          return getAbsoluteImageUrl(img);
        }
        if (img && img.url) {
          return getAbsoluteImageUrl(img.url);
        }
        return img;
      }).filter(Boolean)
    : defaultProduct.images;
  
  // Update productData with processed images
  const finalProductData = {
    ...productData,
    images: processedImages
  };

  const handleImageChange = (direction) => {
    if (direction === 'next') {
      setSelectedImage((prev) => (prev + 1) % finalProductData.images.length);
    } else {
      setSelectedImage((prev) => (prev - 1 + finalProductData.images.length) % finalProductData.images.length);
    }
  };

const handleQuantityChange = (change) => {
    setQuantity(prev => Math.max(1, Math.min(finalProductData.stockCount, prev + change)));
  };

  const { addToCart, loading } = useCart();
  const { isAuthenticated, user } = useAuth();
  const navigate = useNavigate();

  // Check if user is vendor
  const isVendor = user?.userType === 'vendor';

  const handleAddToCart = async () => {
    // Check if user is logged in
    if (!isAuthenticated) {
      notification.warning({
        message: 'Login Required',
        description: 'Please log in to add items to your cart',
        placement: 'topRight',
        duration: 4,
      });
      navigate('/auth');
      return;
    }

    // Check if user is vendor
    if (isVendor) {
      notification.warning({
        message: 'Feature Not Available',
        description: 'Cart functionality is only available for customers. Vendors cannot add items to cart.',
        placement: 'topRight',
        duration: 5,
      });
      return;
    }

    // Validate product data
    const productId = finalProductData._id || finalProductData.id;
    if (!productId) {
      notification.error({
        message: 'Product Error',
        description: 'Product ID not found. Please try again.',
        placement: 'topRight',
        duration: 4,
      });
      return;
    }

    // Check if product is in stock
    if (!finalProductData.inStock || finalProductData.stockCount < quantity) {
      notification.warning({
        message: 'Out of Stock',
        description: `Only ${finalProductData.stockCount || 0} items available`,
        placement: 'topRight',
        duration: 4,
      });
      return;
    }

    try {
      const variantSku = selectedVariant ? finalProductData.variants?.find(v => v.id === selectedVariant)?.sku : null;
      
      await addToCart(productId, quantity, variantSku);
      // Success notification is handled by the CartContext
    } catch (error) {
      // Error notification is handled by the CartContext
      console.error('Error adding to cart:', error);
    }
  };

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, index) => (
      <StarSolidIcon
        key={index}
        className={`w-4 h-4 ${
          index < Math.floor(rating) ? 'text-yellow-400' : 'text-gray-300'
        }`}
      />
    ));
  };

  const renderSpecifications = () => (
    <div className="space-y-6">
      {Object.entries(productData.specifications).map(([category, specs]) => (
        <div key={category}>
          <h4 className="font-semibold text-gray-900 mb-3">{category}</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {Object.entries(specs).map(([key, value]) => (
              <div key={key} className="flex justify-between py-1 border-b border-gray-100">
                <span className="text-gray-600 text-sm">{key}:</span>
                <span className="text-gray-900 text-sm font-medium">{value}</span>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <div className="max-w-7xl mx-auto p-4 lg:p-8">
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 p-6 lg:p-8">
          
          {/* Image Gallery Section */}
          <div className="space-y-4">
            {/* Main Image */}
            <div className="relative aspect-square bg-gray-100 rounded-lg overflow-hidden group">
              <img
                src={finalProductData.images[selectedImage]}
                alt={finalProductData.name}
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
              />
              
              {/* Image Navigation */}
              <button
                onClick={() => handleImageChange('prev')}
                className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-md transition-all"
              >
                <ChevronLeftIcon className="w-5 h-5" />
              </button>
              <button
                onClick={() => handleImageChange('next')}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-md transition-all"
              >
                <ChevronRightIcon className="w-5 h-5" />
              </button>

              {/* Zoom Icon */}
              <div className="absolute top-4 right-4 bg-white/80 rounded-full p-2">
                <MagnifyingGlassIcon className="w-5 h-5" />
              </div>

              {/* Badges */}
              <div className="absolute top-4 left-4 space-y-2">
                {productData.badges.map((badge, index) => (
                  <span
                    key={index}
                    className="inline-block bg-red-500 text-white text-xs font-semibold px-2 py-1 rounded"
                  >
                    {badge}
                  </span>
                ))}
                {productData.discount > 0 && (
                  <span className="inline-block bg-green-500 text-white text-xs font-semibold px-2 py-1 rounded">
                    -{productData.discount}%
                  </span>
                )}
              </div>
            </div>

            {/* Thumbnail Images */}
            <div className="flex space-x-2 overflow-x-auto">
              {finalProductData.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all ${
                    selectedImage === index ? 'border-blue-500' : 'border-gray-200'
                  }`}
                >
                  <img
                    src={image}
                    alt={`${finalProductData.name} ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          </div>

          {/* Product Information Section */}
          <div className="space-y-6">
            {/* Brand and Title */}
            <div>
              <p className="text-blue-600 font-medium text-sm">{productData.brand}</p>
              <h1 className="text-2xl lg:text-3xl font-bold text-gray-900 mt-1">
                {productData.name}
              </h1>
              <p className="text-gray-500 text-sm mt-1">SKU: {productData.sku}</p>
            </div>

            {/* Rating and Reviews */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-1">
                {renderStars(productData.rating)}
                <span className="text-sm font-medium text-gray-900 ml-1">
                  {productData.rating}
                </span>
              </div>
              <span className="text-sm text-gray-500">
                ({productData.reviewCount.toLocaleString()} reviews)
              </span>
            </div>

            {/* Price */}
            <div className="flex items-center space-x-3">
              <span className="text-3xl font-bold text-gray-900">
                ${productData.price}
              </span>
              {productData.originalPrice > productData.price && (
                <span className="text-lg text-gray-500 line-through">
                  ${productData.originalPrice}
                </span>
              )}
              {productData.discount > 0 && (
                <span className="bg-red-100 text-red-800 text-sm font-semibold px-2 py-1 rounded">
                  Save {productData.discount}%
                </span>
              )}
            </div>

            {/* Stock Status */}
            <div className="flex items-center space-x-2">
              {productData.inStock ? (
                <>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-green-600 font-medium">
                    In Stock ({productData.stockCount} available)
                  </span>
                </>
              ) : (
                <>
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span className="text-red-600 font-medium">Out of Stock</span>
                </>
              )}
            </div>

            {/* Variants */}
            {productData.variants && (
              <div>
                <h3 className="text-sm font-medium text-gray-900 mb-3">Color</h3>
                <div className="flex space-x-2">
                  {productData.variants.map((variant) => (
                    <button
                      key={variant.id}
                      onClick={() => setSelectedVariant(variant.id)}
                      disabled={!variant.available}
                      className={`w-8 h-8 rounded-full border-2 transition-all ${
                        selectedVariant === variant.id
                          ? 'border-blue-500 scale-110'
                          : 'border-gray-300'
                      } ${!variant.available ? 'opacity-50 cursor-not-allowed' : 'hover:scale-105'}`}
                      style={{ backgroundColor: variant.color }}
                      title={variant.name}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Quantity and Actions */}
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="flex items-center border border-gray-300 rounded-lg">
                  <button
                    onClick={() => handleQuantityChange(-1)}
                    className="px-3 py-2 hover:bg-gray-100 transition-colors"
                  >
                    -
                  </button>
                  <span className="px-4 py-2 border-x border-gray-300">{quantity}</span>
                  <button
                    onClick={() => handleQuantityChange(1)}
                    className="px-3 py-2 hover:bg-gray-100 transition-colors"
                  >
                    +
                  </button>
                </div>
                <span className="text-sm text-gray-500">
                  Max: {productData.stockCount}
                </span>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-3">
                <button
                  onClick={handleAddToCart} 
                  disabled={!productData.inStock || loading || isVendor}
                  className={`flex-1 font-semibold py-3 px-6 rounded-lg transition-colors flex items-center justify-center space-x-2 ${
                    isVendor 
                      ? 'bg-gray-400 text-white cursor-not-allowed' 
                      : 'bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white'
                  }`}
                >
                  <ShoppingCartIcon className="w-5 h-5" />
                  <span>
                    {!isAuthenticated 
                      ? 'Login to Add to Cart' 
                      : isVendor 
                        ? 'Vendors Cannot Add to Cart' 
                        : 'Add to Cart'
                    }
                  </span>
                </button>
                
                <button className="p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                  <ShareIcon className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* Shipping Info */}
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <div className="flex items-center space-x-3">
                <TruckIcon className="w-5 h-5 text-green-600" />
                <div>
                  <p className="font-medium text-gray-900">
                    {productData.shipping.freeShipping ? 'Free Shipping' : 'Shipping Available'}
                  </p>
                  <p className="text-sm text-gray-600">
                    Estimated delivery: {productData.shipping.estimatedDays} business days
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <ArrowPathIcon className="w-5 h-5 text-blue-600" />
                <div>
                  <p className="font-medium text-gray-900">Easy Returns</p>
                  <p className="text-sm text-gray-600">{productData.shipping.returnPolicy}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <ShieldCheckIcon className="w-5 h-5 text-purple-600" />
                <div>
                  <p className="font-medium text-gray-900">Warranty Included</p>
                  <p className="text-sm text-gray-600">1-year manufacturer warranty</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Product Details Tabs */}
        <div className="border-t border-gray-200">
          <div className="flex border-b border-gray-200">
            {['description', 'specifications', 'reviews'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`px-6 py-4 font-medium text-sm capitalize transition-colors ${
                  activeTab === tab
                    ? 'text-blue-600 border-b-2 border-blue-600'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                {tab}
              </button>
            ))}
          </div>

          <div className="p-6 lg:p-8">
            {activeTab === 'description' && (
              <div className="space-y-4">
                <p className="text-gray-700 leading-relaxed">{productData.description}</p>
                
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Key Features</h4>
                  <ul className="space-y-2">
                    {productData.features.map((feature, index) => (
                      <li key={index} className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            )}

            {activeTab === 'specifications' && renderSpecifications()}

            {activeTab === 'reviews' && (
              <div className="space-y-6">
                {/* Reviews will be handled by ProductDetailPage */}
                <div className="text-center py-8">
                  <p className="text-gray-500">Loading reviews...</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductInfoCard;