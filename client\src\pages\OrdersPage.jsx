import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Typography,
  Table,
  Tag,
  Button,
  Space,
  Spin,
  Alert,
  Empty,
  Row,
  Col,
  Statistic,
  Descriptions,
  Timeline,
  Drawer,
  notification
} from 'antd';
import {
  EyeOutlined,
  TruckOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  EnvironmentOutlined,
  ShoppingCartOutlined
} from '@ant-design/icons';
import Header from '../components/Header';
import Footer from '../components/Footer';
import { orderApi } from '../services/orderApi';
import { orderTrackingApi } from '../services/orderTrackingApi';
import { useAuth } from '../hooks/useAuth';

const { Title, Text } = Typography;

const OrdersPage = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();
  const orderIdParam = searchParams.get('orderId');

  const [orders, setOrders] = useState([]);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [orderDetails, setOrderDetails] = useState(null);
  const [loading, setLoading] = useState(true);
  const [detailsLoading, setDetailsLoading] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [error, setError] = useState(null);

  const statusColors = {
    'pending': 'orange',
    'confirmed': 'blue',
    'processing': 'cyan',
    'shipped': 'purple',
    'delivered': 'green',
    'cancelled': 'red',
    'returned': 'magenta'
  };

  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/auth');
      return;
    }
    fetchOrders();
    
    // If specific order ID is provided, show details
    if (orderIdParam) {
      fetchOrderDetails(orderIdParam);
    }
  }, [isAuthenticated, orderIdParam]);

  const fetchOrders = async () => {
    setLoading(true);
    try {
      const response = await orderApi.getCustomerOrders({ limit: 50 });
      setOrders(response.data.orders || []);
    } catch (err) {
      setError(err.message);
      notification.error({
        message: 'Error',
        description: 'Failed to load orders'
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchOrderDetails = async (orderId) => {
    setDetailsLoading(true);
    try {
      const response = await orderApi.getOrderById(orderId);
      setOrderDetails(response.data);
      setDrawerVisible(true);
    } catch (err) {
      notification.error({
        message: 'Error',
        description: 'Failed to load order details'
      });
    } finally {
      setDetailsLoading(false);
    }
  };

  const handleViewDetails = (orderId) => {
    setSelectedOrder(orderId);
    fetchOrderDetails(orderId);
  };

  const handleTrackOrder = (order) => {
    if (order.tracking?.length > 0) {
      navigate(`/track-order/${order.tracking[0].trackingNumber}`);
    } else {
      navigate(`/track-order?orderId=${order._id}`);
    }
  };

  const columns = [
    {
      title: 'Order #',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      render: (text) => <Text strong>{text}</Text>
    },
    {
      title: 'Date',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => new Date(date).toLocaleDateString()
    },
    {
      title: 'Items',
      dataIndex: 'items',
      key: 'items',
      render: (items) => `${items?.length || 0} items`
    },
    {
      title: 'Total',
      dataIndex: ['pricing', 'total'],
      key: 'total',
      render: (total) => (
        <Text strong type="success">
          ₹{total || 0}
        </Text>
      )
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={statusColors[status]}>
          {status?.replace('_', ' ').toUpperCase()}
        </Tag>
      )
    },
    {
      title: 'Tracking',
      dataIndex: 'tracking',
      key: 'tracking',
      render: (tracking) => {
        if (!tracking?.length) return 'No tracking';
        return (
          <Space direction="vertical" size="small">
            {tracking.map((track, index) => (
              <Tag key={index} color="blue">
                {track.trackingNumber}
              </Tag>
            ))}
          </Space>
        );
      }
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetails(record._id)}
          >
            Details
          </Button>
          <Button
            type="link"
            icon={<TruckOutlined />}
            onClick={() => handleTrackOrder(record)}
          >
            Track
          </Button>
        </Space>
      )
    }
  ];

  const renderOrderDetails = () => {
    if (!orderDetails) return null;

    const { order, tracking } = orderDetails;

    return (
      <div>
        <Descriptions title="Order Information" bordered column={2}>
          <Descriptions.Item label="Order Number">
            {order.orderNumber}
          </Descriptions.Item>
          <Descriptions.Item label="Date">
            {new Date(order.createdAt).toLocaleString()}
          </Descriptions.Item>
          <Descriptions.Item label="Status">
            <Tag color={statusColors[order.status]}>
              {order.status?.replace('_', ' ').toUpperCase()}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Total Amount">
            <Text strong type="success">
              ₹{order.pricing?.total || 0}
            </Text>
          </Descriptions.Item>
        </Descriptions>

        <div className="mt-6">
          <Title level={4}>Order Items</Title>
          <Table
            dataSource={order.items}
            pagination={false}
            size="small"
            columns={[
              {
                title: 'Product',
                dataIndex: 'name',
                key: 'name'
              },
              {
                title: 'Quantity',
                dataIndex: 'quantity',
                key: 'quantity'
              },
              {
                title: 'Price',
                dataIndex: 'unitPrice',
                key: 'unitPrice',
                render: (price) => `₹${price}`
              },
              {
                title: 'Total',
                dataIndex: 'totalPrice',
                key: 'totalPrice',
                render: (total) => `₹${total}`
              }
            ]}
          />
        </div>

        {tracking?.length > 0 && (
          <div className="mt-6">
            <Title level={4}>Tracking Information</Title>
            {tracking.map((track, index) => (
              <Card key={index} className="mb-4">
                <div className="mb-4">
                  <Text strong>Tracking Number: </Text>
                  <Text>{track.trackingNumber}</Text>
                  <div className="mt-2">
                    <Text strong>Carrier: </Text>
                    <Text>{track.carrier?.name}</Text>
                  </div>
                  <div className="mt-2">
                    <Text strong>Status: </Text>
                    <Tag color={statusColors[track.currentStatus]}>
                      {track.currentStatus?.replace('_', ' ').toUpperCase()}
                    </Tag>
                  </div>
                </div>
                
                <Timeline>
                  {track.trackingSteps?.map((step, stepIndex) => (
                    <Timeline.Item key={stepIndex}>
                      <div>
                        <Text strong>{step.title}</Text>
                        <br />
                        <Text>{step.description}</Text>
                        <br />
                        <Text type="secondary">
                          {new Date(step.timestamp).toLocaleString()}
                        </Text>
                      </div>
                    </Timeline.Item>
                  ))}
                </Timeline>
              </Card>
            ))}
          </div>
        )}

        <div className="mt-6">
          <Title level={4}>Shipping Address</Title>
          <Descriptions bordered column={1}>
            <Descriptions.Item label="Name">
              {order.shipping?.firstName} {order.shipping?.lastName}
            </Descriptions.Item>
            <Descriptions.Item label="Address">
              {order.shipping?.address?.street}, {order.shipping?.address?.city}, {order.shipping?.address?.state} {order.shipping?.address?.zipCode}, {order.shipping?.address?.country}
            </Descriptions.Item>
          </Descriptions>
        </div>
      </div>
    );
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <Card>
            <Empty
              description="Please login to view your orders"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            >
              <Button type="primary" onClick={() => navigate('/auth')}>
                Login
              </Button>
            </Empty>
          </Card>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Title level={2}>Order History</Title>
          <Text type="secondary">
            View and manage all your past orders
          </Text>
        </div>

        {loading ? (
          <Card>
            <div className="text-center py-8">
              <Spin size="large" />
              <div className="mt-2">
                <Text>Loading your orders...</Text>
              </div>
            </div>
          </Card>
        ) : error ? (
          <Alert
            message="Error"
            description={error}
            type="error"
            showIcon
            className="mb-6"
          />
        ) : orders.length === 0 ? (
          <Card>
            <Empty
              description="No orders found"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            >
              <Text type="secondary">
                You haven't placed any orders yet.
              </Text>
              <br />
              <Button 
                type="primary" 
                onClick={() => navigate('/')}
                className="mt-4"
              >
                Start Shopping
              </Button>
            </Empty>
          </Card>
        ) : (
          <Card>
            <Table
              dataSource={orders}
              columns={columns}
              rowKey="_id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => 
                  `${range[0]}-${range[1]} of ${total} orders`
              }}
            />
          </Card>
        )}

        <Drawer
          title="Order Details"
          width={800}
          onClose={() => setDrawerVisible(false)}
          open={drawerVisible}
          loading={detailsLoading}
        >
          {renderOrderDetails()}
        </Drawer>
      </div>
      
      <Footer />
    </div>
  );
};

export default OrdersPage;
