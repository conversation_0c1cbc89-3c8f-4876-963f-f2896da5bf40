@echo off
echo Fixing Vite dependency issues...

echo Stopping any running development servers...
taskkill /f /im node.exe 2>nul

echo Cleaning Vite cache...
if exist "node_modules\.vite" (
    rmdir /s /q "node_modules\.vite"
    echo Vite cache cleared
) else (
    echo No Vite cache found
)

echo Cleaning npm cache...
npm cache clean --force

echo Reinstalling dependencies...
npm install

echo Restarting development server...
npm run dev

echo Done!
pause