import React from 'react';
import { Result, <PERSON><PERSON>, <PERSON>, Typography, Space, Divider } from 'antd';
import { CheckCircleOutlined, ShoppingOutlined, TruckOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

const { Title, Text } = Typography;

const OrderSuccess = ({ 
  orderDetails,
  onContinueShopping,
  onTrackOrder 
}) => {
  const navigate = useNavigate();

  const handleContinueShopping = () => {
    if (onContinueShopping) {
      onContinueShopping();
    } else {
      navigate('/');
    }
  };

  const handleTrackOrder = () => {
    if (onTrackOrder) {
      onTrackOrder();
    } else {
      navigate('/orders');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="max-w-2xl w-full">
        <Result
          status="success"
          icon={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
          title={
            <Title level={2} className="text-green-600">
              Order Placed Successfully!
            </Title>
          }
          subTitle={
            <Space direction="vertical" size="small" className="text-center">
              <Text className="text-lg">
                Thank you for your order! Your items will be delivered soon.
              </Text>
              {orderDetails?.orderId && (
                <Text type="secondary">
                  Order ID: <Text strong>#{orderDetails.orderId}</Text>
                </Text>
              )}
              {orderDetails?.estimatedDelivery && (
                <Text type="secondary">
                  Estimated Delivery: <Text strong>{orderDetails.estimatedDelivery}</Text>
                </Text>
              )}
            </Space>
          }
          extra={[
            <Button
              key="track"
              type="primary"
              size="large"
              icon={<TruckOutlined />}
              onClick={handleTrackOrder}
              style={{ backgroundColor: '#1890ff', borderColor: '#1890ff' }}
            >
              Track Your Order
            </Button>,
            <Button
              key="shop"
              size="large"
              icon={<ShoppingOutlined />}
              onClick={handleContinueShopping}
            >
              Continue Shopping
            </Button>
          ]}
        />

        {orderDetails && (
          <div className="mt-6">
            <Divider />
            <div className="bg-gray-50 p-4 rounded">
              <Title level={4} className="mb-3">Order Summary</Title>
              
              {orderDetails.items && (
                <div className="mb-4">
                  <Text strong>Items Ordered:</Text>
                  <div className="mt-2 space-y-2">
                    {orderDetails.items.map((item, index) => (
                      <div key={index} className="flex justify-between items-center">
                        <Text>{item.name} × {item.quantity}</Text>
                        <Text>₹{(item.price * item.quantity).toFixed(0)}</Text>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="space-y-2">
                {orderDetails.totalAmount && (
                  <div className="flex justify-between items-center">
                    <Text strong>Total Amount:</Text>
                    <Text strong className="text-lg">₹{orderDetails.totalAmount.toFixed(0)}</Text>
                  </div>
                )}
                
                {orderDetails.paymentMethod && (
                  <div className="flex justify-between items-center">
                    <Text>Payment Method:</Text>
                    <Text>{orderDetails.paymentMethod === 'cod' ? 'Cash on Delivery' : 'Online Payment'}</Text>
                  </div>
                )}

                {orderDetails.deliveryAddress && (
                  <div className="mt-3">
                    <Text strong>Delivery Address:</Text>
                    <div className="mt-1 p-2 bg-white rounded border">
                      <Text className="block">{orderDetails.deliveryAddress.address}</Text>
                      <Text type="secondary">
                        {orderDetails.deliveryAddress.city}, {orderDetails.deliveryAddress.state} - {orderDetails.deliveryAddress.zipCode}
                      </Text>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
};

export default OrderSuccess;
