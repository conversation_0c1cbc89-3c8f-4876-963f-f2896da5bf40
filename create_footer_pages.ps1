# PowerShell script to create all footer HTML pages

$pages = @(
    @{
        filename = "contact.html"
        title = "Contact Us"
        heading = "Contact Us"
        description = "Get in touch with our support team"
        content = @"
            <div class="contact-section">
                <h2>Get in Touch</h2>
                <div class="contact-methods">
                    <div class="contact-method">
                        <h3>📞 Phone Support</h3>
                        <p><strong>+****************</strong></p>
                        <p>Available 24/7 for immediate assistance</p>
                    </div>
                    <div class="contact-method">
                        <h3>✉️ Email Support</h3>
                        <p><strong><EMAIL></strong></p>
                        <p>We'll respond within 4-24 hours</p>
                    </div>
                    <div class="contact-method">
                        <h3>📍 Visit Our Office</h3>
                        <p><strong>123 Business Ave, Suite 100</strong></p>
                        <p>City, State 12345</p>
                        <p>Mon-Fri: 9:00 AM - 6:00 PM EST</p>
                    </div>
                </div>
                
                <div class="business-hours">
                    <h3>Business Hours</h3>
                    <ul>
                        <li>Monday - Friday: 9:00 AM - 6:00 PM EST</li>
                        <li>Saturday: 10:00 AM - 4:00 PM EST</li>
                        <li>Sunday: Closed</li>
                        <li>Emergency Support: 24/7 Available</li>
                    </ul>
                </div>
            </div>
"@
    },
    @{
        filename = "report.html"
        title = "Report Abuse"
        heading = "Report Abuse or Violation"
        description = "Help us maintain a safe community by reporting violations"
        content = @"
            <div class="report-section">
                <h2>Submit a Report</h2>
                <p>If you've encountered any abuse, fraud, or violations on our platform, please let us know. All reports are confidential and will be investigated promptly.</p>
                
                <div class="report-types">
                    <h3>What can you report?</h3>
                    <ul>
                        <li><strong>Fraudulent Activity:</strong> Fake products, scam attempts, or dishonest sellers</li>
                        <li><strong>Harassment:</strong> Bullying, threats, or inappropriate behavior</li>
                        <li><strong>Intellectual Property:</strong> Copyright or trademark violations</li>
                        <li><strong>Privacy Violations:</strong> Unauthorized use of personal information</li>
                        <li><strong>Other Violations:</strong> Any other concerning behavior or policy violations</li>
                    </ul>
                </div>
                
                <div class="report-contact">
                    <h3>How to Report</h3>
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Phone:</strong> +1 (555) 123-ABUSE</p>
                    <p><strong>Emergency:</strong> +1 (555) 999-HELP</p>
                </div>
                
                <div class="safety-reminder">
                    <h3>⚠️ Safety and Confidentiality</h3>
                    <p>All reports are treated with strict confidentiality. We take violations seriously and handle them with the utmost care to ensure a safe marketplace for everyone.</p>
                </div>
            </div>
"@
    },
    @{
        filename = "dispute.html"
        title = "Submit a Dispute"
        heading = "Submit a Dispute"
        description = "Resolve conflicts with sellers or resolve transaction issues"
        content = @"
            <div class="dispute-section">
                <h2>Dispute Resolution Center</h2>
                <p>Having an issue with an order or seller? Our dispute resolution process helps you resolve conflicts fairly and efficiently.</p>
                
                <div class="dispute-types">
                    <h3>Common Dispute Types</h3>
                    <ul>
                        <li><strong>Item Not Received:</strong> Your order hasn't arrived within the expected timeframe</li>
                        <li><strong>Item Not as Described:</strong> The product doesn't match the listing description</li>
                        <li><strong>Defective Product:</strong> Item arrived damaged or doesn't work properly</li>
                        <li><strong>Unauthorized Charges:</strong> Charges you didn't approve</li>
                        <li><strong>Seller Communication:</strong> Seller is unresponsive or unprofessional</li>
                    </ul>
                </div>
                
                <div class="dispute-process">
                    <h3>Dispute Process</h3>
                    <ol>
                        <li><strong>Contact the Seller:</strong> Try to resolve the issue directly first</li>
                        <li><strong>File a Dispute:</strong> If unsuccessful, submit a formal dispute</li>
                        <li><strong>Investigation:</strong> Our team reviews all evidence and communications</li>
                        <li><strong>Resolution:</strong> We work to find a fair solution for both parties</li>
                    </ol>
                </div>
                
                <div class="dispute-contact">
                    <h3>Submit Your Dispute</h3>
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Phone:</strong> +****************</p>
                    <p><strong>Response Time:</strong> 24-48 hours</p>
                </div>
            </div>
"@
    },
    @{
        filename = "policies.html"
        title = "Policies & Rules"
        heading = "Policies & Rules"
        description = "Our community guidelines and platform policies"
        content = @"
            <div class="policies-section">
                <h2>Community Guidelines & Platform Policies</h2>
                
                <div class="policy-item">
                    <h3>User Conduct Policy</h3>
                    <p>All users must treat others with respect and follow our community standards. Harassment, discrimination, or abusive behavior is not tolerated.</p>
                </div>
                
                <div class="policy-item">
                    <h3>Seller Standards</h3>
                    <p>Sellers must provide accurate product descriptions, ship items promptly, and maintain high customer service standards.</p>
                </div>
                
                <div class="policy-item">
                    <h3>Product Listing Guidelines</h3>
                    <p>All products must be legal, accurately described, and comply with our prohibited items list. Counterfeit or stolen goods are strictly forbidden.</p>
                </div>
                
                <div class="policy-item">
                    <h3>Payment & Refund Policy</h3>
                    <p>Secure payment processing, buyer protection, and clear refund procedures ensure safe transactions for all users.</p>
                </div>
                
                <div class="policy-item">
                    <h3>Privacy Protection</h3>
                    <p>We protect user privacy and data according to applicable laws and our comprehensive privacy policy.</p>
                </div>
                
                <div class="policy-item">
                    <h3>Intellectual Property</h3>
                    <p>Respect for intellectual property rights is mandatory. Report any copyright or trademark violations immediately.</p>
                </div>
                
                <div class="policy-links">
                    <h3>Related Documents</h3>
                    <ul>
                        <li><a href="/pages/privacy.html">Privacy Policy</a></li>
                        <li><a href="/pages/terms.html">Terms of Service</a></li>
                        <li><a href="/pages/cookies.html">Cookie Policy</a></li>
                    </ul>
                </div>
            </div>
"@
    },
    @{
        filename = "shopping-guide.html"
        title = "Online Shopping Guide"
        heading = "Online Shopping Guide"
        description = "Tips and guidance for safe and successful online shopping"
        content = @"
            <div class="shopping-guide">
                <h2>How to Shop Safely Online</h2>
                
                <div class="guide-section">
                    <h3>🔍 Before You Buy</h3>
                    <ul>
                        <li>Read product descriptions carefully</li>
                        <li>Check seller ratings and reviews</li>
                        <li>Compare prices from different sellers</li>
                        <li>Verify shipping costs and delivery times</li>
                    </ul>
                </div>
                
                <div class="guide-section">
                    <h3>💳 Secure Payment</h3>
                    <ul>
                        <li>Use secure payment methods (credit cards, PayPal)</li>
                        <li>Never share your password or personal information</li>
                        <li>Look for SSL encryption (https://)</li>
                        <li>Keep records of your transactions</li>
                    </ul>
                </div>
                
                <div class="guide-section">
                    <h3>📦 After Purchase</h3>
                    <ul>
                        <li>Track your order regularly</li>
                        <li>Inspect items upon delivery</li>
                        <li>Contact seller immediately if there are issues</li>
                        <li>Leave honest reviews to help other buyers</li>
                    </ul>
                </div>
                
                <div class="guide-section">
                    <h3>🛡️ Buyer Protection</h3>
                    <ul>
                        <li>All purchases are covered by our buyer protection program</li>
                        <li>Full refund if items don't arrive or aren't as described</li>
                        <li>Free dispute resolution service</li>
                        <li>24/7 customer support available</li>
                    </ul>
                </div>
                
                <div class="guide-section">
                    <h3>⚠️ Red Flags to Avoid</h3>
                    <ul>
                        <li>Prices that seem too good to be true</li>
                        <li>Suspicious or unprofessional seller communication</li>
                        <li>Requests for payment outside our platform</li>
                        <li>Poor or no seller reviews</li>
                    </ul>
                </div>
            </div>
"@
    }
)

# Create the pages directory if it doesn't exist
$pagesDir = "client/public/pages"
if (!(Test-Path $pagesDir)) {
    New-Item -ItemType Directory -Path $pagesDir -Force
}

# Base HTML template
$baseTemplate = @"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{TITLE}} - Multi-Vendor Marketplace</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f9fafb;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .header {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            color: white;
            padding: 60px 0;
            text-align: center;
        }
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        .main-content {
            padding: 40px 0;
        }
        .content-section {
            background: white;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .content-section h2 {
            color: #1f2937;
            margin-bottom: 20px;
            font-size: 1.8rem;
        }
        .content-section h3 {
            color: #374151;
            margin: 20px 0 10px 0;
            font-size: 1.3rem;
        }
        .content-section ul {
            margin: 15px 0;
            padding-left: 25px;
        }
        .content-section li {
            margin-bottom: 8px;
        }
        .content-section p {
            margin-bottom: 15px;
            color: #6b7280;
            line-height: 1.7;
        }
        .contact-methods, .guide-section, .policy-item, .report-types, .dispute-types {
            margin: 25px 0;
            padding: 20px;
            background: #f9fafb;
            border-radius: 8px;
            border-left: 4px solid #2563eb;
        }
        .contact-method {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }
        .contact-method h3 {
            color: #2563eb;
            margin-bottom: 8px;
        }
        .back-link {
            display: inline-block;
            margin: 20px 0;
            padding: 10px 20px;
            background: #2563eb;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .back-link:hover {
            background: #1d4ed8;
        }
        .safety-reminder {
            background: #fef3c7;
            border-left-color: #f59e0b;
        }
        .policy-links a {
            color: #2563eb;
            text-decoration: none;
        }
        .policy-links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>{{HEADING}}</h1>
            <p>{{DESCRIPTION}}</p>
        </div>
    </div>

    <div class="main-content">
        <div class="container">
            <a href="/" class="back-link">← Back to Home</a>
            
            <div class="content-section">
                {{CONTENT}}
            </div>
        </div>
    </div>
</body>
</html>
"@

# Generate each page
foreach ($page in $pages) {
    $html = $baseTemplate -replace "{{TITLE}}", $page.title
    $html = $html -replace "{{HEADING}}", $page.heading
    $html = $html -replace "{{DESCRIPTION}}", $page.description
    $html = $html -replace "{{CONTENT}}", $page.content
    
    $filePath = Join-Path $pagesDir $page.filename
    $html | Out-File -FilePath $filePath -Encoding UTF8
    
    Write-Host "Created: $($page.filename)"
}

Write-Host "All footer pages created successfully!"
