import { useState, useEffect, useCallback, useRef } from 'react';
import { message } from 'antd';
import { dashboardApi } from '../../../services/vendorApi';
import { config, debugLog, errorLog, infoLog } from '../../../config/environment';

// Environment-based configuration
const MAX_RETRIES = config.api.retryAttempts;
const RETRY_DELAY = config.api.retryDelay;
const CACHE_TIMEOUT = config.analytics.cacheTimeout;
const REFRESH_INTERVAL = config.analytics.refreshInterval;
const ENABLE_REAL_TIME = config.analytics.enableRealTime;

// Helper function to delay execution
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Generate sample analytics data for demonstration
const generateSampleAnalyticsData = (period) => {
  const now = new Date();
  const data = [];
  
  let days;
  switch (period) {
    case '7d':
      days = 7;
      break;
    case '30d':
      days = 30;
      break;
    case '90d':
      days = 90;
      break;
    case '1y':
      days = 365;
      break;
    default:
      days = 30;
  }
  
  // Generate data points for the specified period
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(now);
    date.setDate(date.getDate() - i);
    
    // Generate realistic sample revenue data
    const baseRevenue = Math.random() * 5000 + 1000; // Between 1000-6000
    const weekendMultiplier = date.getDay() === 0 || date.getDay() === 6 ? 1.5 : 1;
    const revenue = Math.round(baseRevenue * weekendMultiplier);
    
    data.push({
      _id: {
        year: date.getFullYear(),
        month: date.getMonth() + 1,
        day: date.getDate()
      },
      revenue,
      orders: Math.floor(revenue / 500) + Math.floor(Math.random() * 3) // Roughly 1 order per 500 revenue
    });
  }
  
  return data;
};

const useAnalyticsData = () => {
  // State
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [dashboardData, setDashboardData] = useState(null);
  const [analyticsData, setAnalyticsData] = useState(null);
  const [selectedPeriod, setSelectedPeriod] = useState('30d');
  const [lastUpdated, setLastUpdated] = useState(null);
  const [error, setError] = useState(null);
  
  // Refs to track component state and prevent memory leaks
  const isMountedRef = useRef(true);
  const abortControllerRef = useRef(null);
  const realTimeIntervalRef = useRef(null);
  const lastFetchRef = useRef(null);
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (realTimeIntervalRef.current) {
        clearInterval(realTimeIntervalRef.current);
      }
    };
  }, []);
  
  // Enhanced fetch function with retry logic
  const fetchWithRetry = useCallback(async (fetchFunction, retries = MAX_RETRIES) => {
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        console.log(`🔄 Attempt ${attempt}/${retries}`);
        const result = await fetchFunction();
        console.log(`✅ Success on attempt ${attempt}`);
        return result;
      } catch (error) {
        console.error(`❌ Attempt ${attempt} failed:`, error.message);
        
        if (attempt === retries) {
          throw error; // Last attempt failed
        }
        
        // Wait before retry
        await delay(RETRY_DELAY * attempt);
      }
    }
  }, []);

  // Fetch dashboard stats with enhanced error handling
  const fetchDashboardStats = useCallback(async () => {
    if (!isMountedRef.current) return;
    
    try {
      setError(null);
      console.log('📊 Fetching dashboard stats...');
      
      const response = await fetchWithRetry(async () => {
        return await dashboardApi.getStats();
      });
      
      console.log('Dashboard API response:', response);
      
      if (response.data.success && isMountedRef.current) {
        console.log('✅ Dashboard data received:', response.data.data);
        setDashboardData(response.data.data);
        setLastUpdated(new Date());
      } else {
        console.log('⚠️ Dashboard API returned unsuccessful response');
        throw new Error(response.data.message || 'Dashboard API returned unsuccessful response');
      }
    } catch (error) {
      if (!isMountedRef.current) return;
      
      console.error('❌ Error fetching dashboard stats:', error);
      let errorMessage = 'Failed to fetch dashboard data.';
      
      if (error.response?.status === 401) {
        errorMessage = 'Session expired. Please log in again.';
        setTimeout(() => {
          localStorage.clear();
          window.location.href = '/auth';
        }, 2000);
      } else if (error.response?.status === 403) {
        errorMessage = 'Access denied. Please check your vendor permissions.';
      } else if (error.response?.status === 500) {
        errorMessage = 'Server error. Please try again later.';
      } else if (error.code === 'NETWORK_ERROR') {
        errorMessage = 'Network error. Please check your internet connection.';
      }
      
      setError(errorMessage);
      
      // Set fallback data with sample data to show dashboard structure
      setDashboardData({
        products: {
          totalProducts: 0,
          activeProducts: 0,
          averageRating: 0
        },
        orders: {
          totalOrders: 0,
          totalRevenue: 0,
          averageOrderValue: 0,
          deliveredOrders: 0
        },
        today: {
          todayOrders: 0,
          todayRevenue: 0
        },
        recentOrders: [],
        topProducts: []
      });
      setLastUpdated(new Date());
    }
  }, [fetchWithRetry]);
  
  // Fetch analytics data with enhanced error handling
  const fetchAnalyticsData = useCallback(async (period = selectedPeriod) => {
    if (!isMountedRef.current) return;
    
    try {
      console.log('📈 Fetching analytics data for period:', period);
      
      const response = await fetchWithRetry(async () => {
        return await dashboardApi.getAnalytics({ period, type: 'revenue' });
      });
      
      console.log('Analytics API response:', response);
      
      if (response.data.success && isMountedRef.current) {
        console.log('✅ Analytics data received:', response.data.data);
        setAnalyticsData(response.data.data);
      } else {
        console.log('⚠️ Analytics API returned unsuccessful response');
        // Set empty analytics data - no fake data
        setAnalyticsData({
          analytics: [],
          period,
          type: 'revenue'
        });
      }
    } catch (error) {
      if (!isMountedRef.current) return;
      
      console.error('❌ Error fetching analytics:', error);
      
      // Set empty analytics data on error - no fake data
      setAnalyticsData({
        analytics: [],
        period,
        type: 'revenue'
      });
    }
  }, [selectedPeriod, fetchWithRetry]);
  
  // Handle refresh
  const handleRefresh = useCallback(async () => {
    if (!isMountedRef.current) return;
    
    setRefreshing(true);
    try {
      await Promise.all([
        fetchDashboardStats(),
        fetchAnalyticsData(selectedPeriod)
      ]);
      
      if (!error && isMountedRef.current) {
        message.success('Analytics refreshed successfully');
      }
    } catch (error) {
      console.error('Error refreshing analytics:', error);
      if (isMountedRef.current) {
        message.error('Failed to refresh analytics');
      }
    } finally {
      if (isMountedRef.current) {
        setRefreshing(false);
      }
    }
  }, [fetchDashboardStats, fetchAnalyticsData, selectedPeriod, error]);
  
  // Handle period change
  const handlePeriodChange = useCallback(async (newPeriod) => {
    if (!isMountedRef.current) return;
    
    setSelectedPeriod(newPeriod);
    await fetchAnalyticsData(newPeriod);
  }, [fetchAnalyticsData]);
  
  // Initialize data on mount
  useEffect(() => {
    const loadDashboard = async () => {
      if (!isMountedRef.current) return;

      console.log('🚀 Initializing analytics dashboard...');
      setLoading(true);
      setError(null);

      // Set default fallback data immediately to prevent undefined states
      const defaultDashboardData = {
        products: {
          totalProducts: 0,
          activeProducts: 0,
          averageRating: 0
        },
        orders: {
          totalOrders: 0,
          totalRevenue: 0,
          averageOrderValue: 0,
          deliveredOrders: 0
        },
        today: {
          todayOrders: 0,
          todayRevenue: 0
        },
        recentOrders: [],
        topProducts: []
      };

      const defaultAnalyticsData = {
        analytics: [],
        period: selectedPeriod,
        type: 'revenue'
      };

      try {
        // Fetch dashboard stats with shorter timeout
        let dashboardSuccess = false;
        try {
          console.log('📊 Fetching dashboard stats...');

          const dashboardResponse = await Promise.race([
            dashboardApi.getStats(),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Dashboard API timeout')), 15000)
            )
          ]);

          console.log('Dashboard API response:', dashboardResponse);

          if (dashboardResponse?.data?.success && isMountedRef.current) {
            console.log('✅ Dashboard data received:', dashboardResponse.data.data);
            setDashboardData(dashboardResponse.data.data);
            dashboardSuccess = true;
          }
        } catch (dashboardError) {
          console.error('❌ Error fetching dashboard stats:', dashboardError);
          setError('Dashboard data unavailable. Showing default values.');
        }

        // Set fallback data if dashboard fetch failed
        if (!dashboardSuccess && isMountedRef.current) {
          setDashboardData(defaultDashboardData);
        }

        // Fetch analytics data with shorter timeout
        try {
          console.log('📈 Fetching analytics data for period:', selectedPeriod);

          const analyticsResponse = await Promise.race([
            dashboardApi.getAnalytics({ period: selectedPeriod, type: 'revenue' }),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Analytics API timeout')), 15000)
            )
          ]);

          console.log('Analytics API response:', analyticsResponse);

          if (analyticsResponse?.data?.success && isMountedRef.current) {
            console.log('✅ Analytics data received:', analyticsResponse.data.data);
            setAnalyticsData(analyticsResponse.data.data);
          } else if (isMountedRef.current) {
            setAnalyticsData(defaultAnalyticsData);
          }
        } catch (analyticsError) {
          console.error('❌ Error fetching analytics:', analyticsError);
          if (isMountedRef.current) {
            setAnalyticsData(defaultAnalyticsData);
          }
        }

        // Always set last updated time
        if (isMountedRef.current) {
          setLastUpdated(new Date());
        }

      } catch (error) {
        console.error('❌ Error initializing analytics:', error);
        if (isMountedRef.current) {
          setError('Failed to load analytics data. Please try refreshing the page.');
          setDashboardData(defaultDashboardData);
          setAnalyticsData(defaultAnalyticsData);
          setLastUpdated(new Date());
        }
      } finally {
        // Always set loading to false, regardless of success or failure
        if (isMountedRef.current) {
          console.log('✅ Analytics initialization complete');
          setLoading(false);
        }
      }
    };

    loadDashboard();
  }, []); // Remove function dependencies to prevent infinite loop
  
  // Real-time updates effect
  useEffect(() => {
    if (!ENABLE_REAL_TIME || !isMountedRef.current) {
      return;
    }
    
    const setupRealTimeUpdates = () => {
      debugLog('🔄 Setting up real-time analytics updates', { interval: REFRESH_INTERVAL });
      
      realTimeIntervalRef.current = setInterval(async () => {
        if (!isMountedRef.current) {
          debugLog('⚠️ Component unmounted, stopping real-time updates');
          clearInterval(realTimeIntervalRef.current);
          return;
        }
        
        const now = Date.now();
        const lastFetch = lastFetchRef.current;
        
        // Prevent too frequent updates
        if (lastFetch && (now - lastFetch) < CACHE_TIMEOUT) {
          debugLog('⏱️ Skipping real-time update due to cache timeout');
          return;
        }
        
        debugLog('🔄 Real-time analytics update triggered');
        lastFetchRef.current = now;
        
        try {
          // Only refresh data, don't show loading state for real-time updates
          await Promise.all([
            fetchDashboardStats(),
            fetchAnalyticsData(selectedPeriod)
          ]);
          
          debugLog('✅ Real-time update completed successfully');
        } catch (error) {
          errorLog('❌ Real-time update failed:', error);
          // Don't show error messages for background updates
        }
      }, REFRESH_INTERVAL);
    };
    
    // Start real-time updates after initial load
    if (!loading && dashboardData) {
      setupRealTimeUpdates();
    }
    
    return () => {
      if (realTimeIntervalRef.current) {
        clearInterval(realTimeIntervalRef.current);
        debugLog('🛑 Real-time updates stopped');
      }
    };
  }, [loading, dashboardData, selectedPeriod, fetchDashboardStats, fetchAnalyticsData]);
  
  return {
    // State
    loading,
    refreshing,
    dashboardData,
    analyticsData,
    selectedPeriod,
    lastUpdated,
    error,
    
    // Actions
    handleRefresh,
    handlePeriodChange,
    setError
  };
};

export default useAnalyticsData;
