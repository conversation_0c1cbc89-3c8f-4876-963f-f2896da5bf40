import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { Layout, Card, Row, Col, Statistic, Typography, Space, Avatar, Badge, Progress, Spin, message, Button, Select, DatePicker, Alert, Empty } from 'antd';
import {
  ShoppingCartOutlined,
  DollarOutlined,
  ProductOutlined,
  EyeOutlined,
  RiseOutlined,
  StarOutlined,
  ReloadOutlined,
  WarningOutlined
} from '@ant-design/icons';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { Line, Bar, Pie } from 'react-chartjs-2';
import { dashboardApi, ordersApi } from '../../services/vendorApi';
import useResponsive from '../../hooks/useResponsive';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

// Extend dayjs with relativeTime plugin
dayjs.extend(relativeTime);

const { Content } = Layout;
const { Title: AntTitle } = Typography;

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

// Cache for API responses to prevent duplicate calls
const apiCache = new Map();
const CACHE_DURATION = 30000; // 30 seconds cache

// Request deduplication map
const pendingRequests = new Map();

// Component instance counter to track multiple instances
let componentInstanceCounter = 0;

const ResponsiveVendorDashboard = () => {
  const { isMobile, isTablet } = useResponsive();
  
  // State for real-time data
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [dashboardData, setDashboardData] = useState(null);
  const [analyticsData, setAnalyticsData] = useState(null);
  const [selectedPeriod, setSelectedPeriod] = useState('30d');
  const [lastUpdated, setLastUpdated] = useState(null);
  const [error, setError] = useState(null);
  
  // Refs to track component state and prevent memory leaks
  const isMountedRef = useRef(true);
  const hasInitializedRef = useRef(false);
  const abortControllerRef = useRef(null);
  const instanceIdRef = useRef(++componentInstanceCounter);
  
  // Log component lifecycle
  useEffect(() => {
    console.log(`🚀 VendorDashboard instance ${instanceIdRef.current} mounted at`, new Date().toISOString());
    
    return () => {
      console.log(`🔥 VendorDashboard instance ${instanceIdRef.current} unmounting at`, new Date().toISOString());
      isMountedRef.current = false;
      
      // Abort any pending requests
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      
      // Clear any pending requests for this instance
      for (const [key, promise] of pendingRequests.entries()) {
        if (key.includes(`instance-${instanceIdRef.current}`)) {
          pendingRequests.delete(key);
        }
      }
    };
  }, []);

  // Optimized fetch function with caching and deduplication
  const fetchWithCache = useCallback(async (cacheKey, fetchFunction, forceRefresh = false) => {
    // Check if component is still mounted
    if (!isMountedRef.current) {
      console.log(`⚠️ Instance ${instanceIdRef.current} unmounted, skipping API call:`, cacheKey);
      return null;
    }

    // Add instance ID to cache key to prevent cross-instance conflicts
    const instanceCacheKey = `${cacheKey}-instance-${instanceIdRef.current}`;

    // Check cache first (unless force refresh)
    if (!forceRefresh) {
      const cached = apiCache.get(cacheKey); // Use global cache key for shared data
      if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
        console.log(`📋 Instance ${instanceIdRef.current} using cached data for:`, cacheKey);
        return cached.data;
      }
    }

    // Check if request is already pending (use global key to prevent duplicate requests)
    if (pendingRequests.has(cacheKey)) {
      console.log(`⏳ Instance ${instanceIdRef.current} waiting for pending request:`, cacheKey);
      try {
        return await pendingRequests.get(cacheKey);
      } catch (error) {
        console.error(`❌ Instance ${instanceIdRef.current} pending request failed:`, cacheKey, error);
        return null;
      }
    }

    // Create new request
    const requestPromise = (async () => {
      try {
        console.log(`🌐 Instance ${instanceIdRef.current} making API call:`, cacheKey, new Date().toISOString());
        
        // Abort previous request if exists
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
        }
        
        // Create new abort controller
        abortControllerRef.current = new AbortController();
        
        const result = await fetchFunction();
        
        // Only cache if component is still mounted
        if (isMountedRef.current) {
          // Cache the result globally
          apiCache.set(cacheKey, {
            data: result,
            timestamp: Date.now()
          });
          
          console.log(`✅ Instance ${instanceIdRef.current} API call successful:`, cacheKey);
        }
        
        return result;
      } catch (error) {
        if (error.name === 'AbortError') {
          console.log(`🛑 Instance ${instanceIdRef.current} request aborted:`, cacheKey);
          return null;
        }
        console.error(`❌ Instance ${instanceIdRef.current} API call failed:`, cacheKey, error);
        throw error;
      } finally {
        // Remove from pending requests
        pendingRequests.delete(cacheKey);
      }
    })();

    // Store pending request globally to prevent duplicates
    pendingRequests.set(cacheKey, requestPromise);

    return await requestPromise;
  }, []);

  // Fetch dashboard stats with caching
  const fetchDashboardStats = useCallback(async (forceRefresh = false) => {
    if (!isMountedRef.current) return;

    try {
      setError(null);
      
      const result = await fetchWithCache(
        'dashboard-stats',
        async () => {
          const response = await dashboardApi.getStats();
          if (response.data.success) {
            return response.data.data;
          } else {
            throw new Error(response.data.message || 'Failed to fetch dashboard data');
          }
        },
        forceRefresh
      );

      if (result && isMountedRef.current) {
        setDashboardData(result);
        setLastUpdated(new Date());
      }
    } catch (error) {
      if (!isMountedRef.current) return;
      
      console.error(`❌ Instance ${instanceIdRef.current} error fetching dashboard stats:`, error);
      setError('Failed to fetch dashboard data. Please check your connection and try again.');
      
      // Set fallback data with sample data to show dashboard functionality
      const sampleDashboardData = generateSampleDashboardData();
      setDashboardData(sampleDashboardData);
    }
  }, [fetchWithCache]);
  
  // Fetch analytics data with caching
  const fetchAnalyticsData = useCallback(async (period = selectedPeriod, forceRefresh = false) => {
    if (!isMountedRef.current) return;

    try {
      const result = await fetchWithCache(
        `analytics-${period}-revenue`,
        async () => {
          const response = await dashboardApi.getAnalytics({ period, type: 'revenue' });
          if (response.data.success) {
            return response.data.data;
          } else {
            throw new Error(response.data.message || 'Failed to fetch analytics data');
          }
        },
        forceRefresh
      );

      if (result && isMountedRef.current) {
        setAnalyticsData(result);
      }
    } catch (error) {
      if (!isMountedRef.current) return;
      
      console.error(`❌ Instance ${instanceIdRef.current} error fetching analytics:`, error);
      
      // Set fallback analytics data with sample data
      const sampleAnalyticsData = generateSampleAnalyticsData(period);
      setAnalyticsData({
        analytics: sampleAnalyticsData,
        period,
        type: 'revenue'
      });
    }
  }, [selectedPeriod, fetchWithCache]);
  
  // Handle refresh - force refresh with cache bypass
  const handleRefresh = useCallback(async () => {
    if (!isMountedRef.current) return;
    
    setRefreshing(true);
    try {
      console.log(`🔄 Instance ${instanceIdRef.current} manual refresh triggered...`);
      
      // Clear cache for fresh data
      apiCache.delete('dashboard-stats');
      apiCache.delete(`analytics-${selectedPeriod}-revenue`);
      
      await Promise.all([
        fetchDashboardStats(true),
        fetchAnalyticsData(selectedPeriod, true)
      ]);
      
      if (!error && isMountedRef.current) {
        message.success('Dashboard refreshed successfully');
      }
    } catch (error) {
      console.error(`❌ Instance ${instanceIdRef.current} error refreshing dashboard:`, error);
      if (isMountedRef.current) {
        message.error('Failed to refresh dashboard');
      }
    } finally {
      if (isMountedRef.current) {
        setRefreshing(false);
      }
    }
  }, [fetchDashboardStats, fetchAnalyticsData, selectedPeriod, error]);
  
  // Handle period change
  const handlePeriodChange = useCallback(async (newPeriod) => {
    if (!isMountedRef.current) return;
    
    console.log(`📊 Instance ${instanceIdRef.current} period changed to:`, newPeriod);
    setSelectedPeriod(newPeriod);
    await fetchAnalyticsData(newPeriod);
  }, [fetchAnalyticsData]);
  
  // Initialize dashboard on mount - StrictMode compatible
  useEffect(() => {
    let cancelled = false;
    
    const loadDashboard = async () => {
      // Prevent double initialization in StrictMode
      if (hasInitializedRef.current || cancelled || !isMountedRef.current) {
        console.log(`⚠️ Instance ${instanceIdRef.current} initialization skipped - already initialized: ${hasInitializedRef.current}, cancelled: ${cancelled}, mounted: ${isMountedRef.current}`);
        return;
      }
      
      hasInitializedRef.current = true;
      
      setLoading(true);
      setError(null);
      
      // Enhanced debugging information
      console.log(`📊 Instance ${instanceIdRef.current} loading dashboard data (StrictMode compatible)...`, new Date().toISOString());
      console.log('🔍 Debug Info:', {
        token: localStorage.getItem('authToken') ? 'Present' : 'Missing',
        user: localStorage.getItem('authUser') ? JSON.parse(localStorage.getItem('authUser')) : null,
        apiUrl: import.meta.env.VITE_API_URL || 'Using default'
      });
      
      try {
        // Add timeout for the entire operation
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Dashboard loading timed out after 30 seconds')), 30000);
        });
        
        const loadPromise = Promise.all([
          fetchDashboardStats(),
          fetchAnalyticsData()
        ]);
        
        await Promise.race([loadPromise, timeoutPromise]);
        
        console.log(`✅ Instance ${instanceIdRef.current} dashboard loaded successfully`);
      } catch (error) {
        if (!cancelled && isMountedRef.current) {
          console.error(`❌ Instance ${instanceIdRef.current} error initializing dashboard:`, error);
          
          // Set user-friendly error message
          let errorMessage = 'Failed to load dashboard data. ';
          
          if (error.message.includes('timeout')) {
            errorMessage += 'The request timed out. Please check your internet connection.';
          } else if (error.message.includes('Network Error')) {
            errorMessage += 'Unable to connect to the server. Please check your internet connection.';
          } else if (error.response?.status === 401) {
            errorMessage += 'Your session has expired. Please log in again.';
            // Redirect to login after a delay
            setTimeout(() => {
              localStorage.clear();
              window.location.href = '/auth';
            }, 3000);
          } else if (error.response?.status === 404) {
            errorMessage += 'Vendor account not found. Please contact support.';
          } else if (error.response?.status >= 500) {
            errorMessage += 'Server error occurred. Please try again later.';
          } else {
            errorMessage += 'Please try refreshing the page.';
          }
          
          setError(errorMessage);
        }
      } finally {
        if (!cancelled && isMountedRef.current) {
          setLoading(false);
        }
      }
    };
    
    loadDashboard();
    
    return () => {
      cancelled = true;
    };
  }, []); // Empty dependency array - runs only once on mount
  
  // Format currency
  const formatCurrency = useCallback((amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount || 0);
  }, []);
  
  // Memoized chart data to prevent unnecessary recalculations
  const salesPerformanceData = useMemo(() => {
    if (!analyticsData?.analytics) {
      return {
        labels: [],
        datasets: [{
          label: 'Sales (₹)',
          data: [],
          borderColor: '#52c41a',
          backgroundColor: 'rgba(82, 196, 26, 0.1)',
          fill: true,
          tension: 0.4,
        }]
      };
    }
    
    const data = analyticsData.analytics;
    const labels = data.map(item => {
      if (item._id.day) {
        return dayjs(`${item._id.year}-${item._id.month}-${item._id.day}`).format('MMM DD');
      }
      return dayjs(`${item._id.year}-${item._id.month}-01`).format('MMM YYYY');
    });
    
    return {
      labels,
      datasets: [{
        label: 'Sales (₹)',
        data: data.map(item => item.revenue || 0),
        borderColor: '#52c41a',
        backgroundColor: 'rgba(82, 196, 26, 0.1)',
        fill: true,
        tension: 0.4,
      }]
    };
  }, [analyticsData]);
  
  const productPerformanceData = useMemo(() => {
    const topProducts = dashboardData?.topProducts || [];
    if (!topProducts.length) {
      return {
        labels: ['No Data'],
        datasets: [{
          label: 'Units Sold',
          data: [0],
          backgroundColor: ['#cccccc']
        }]
      };
    }
    
    return {
      labels: topProducts.map(product => product.name),
      datasets: [{
        label: 'Units Sold',
        data: topProducts.map(product => product.sales?.totalSold || 0),
        backgroundColor: [
          '#1890ff',
          '#52c41a',
          '#faad14',
          '#f5222d',
          '#722ed1',
          '#13c2c2',
        ],
      }]
    };
  }, [dashboardData]);
  
  const revenueBreakdownData = useMemo(() => {
    const orders = dashboardData?.orders;
    if (!orders) {
      return {
        labels: ['Product Sales', 'Shipping', 'Commission', 'Returns'],
        datasets: [{
          data: [75, 15, 8, 2],
          backgroundColor: ['#52c41a', '#1890ff', '#faad14', '#f5222d']
        }]
      };
    }
    
    const totalRevenue = orders.totalRevenue || 1;
    const productSales = totalRevenue * 0.85;
    const shipping = totalRevenue * 0.10;
    const commission = totalRevenue * 0.03;
    const returns = totalRevenue * 0.02;
    
    return {
      labels: ['Product Sales', 'Shipping', 'Commission', 'Returns'],
      datasets: [{
        data: [productSales, shipping, commission, returns],
        backgroundColor: ['#52c41a', '#1890ff', '#faad14', '#f5222d']
      }]
    };
  }, [dashboardData]);
  
  const topSellingProductsData = useMemo(() => {
    const topProducts = dashboardData?.topProducts || [];
    if (!topProducts.length) {
      return {
        labels: ['No Data'],
        datasets: [{
          label: 'Revenue (₹)',
          data: [0],
          backgroundColor: '#cccccc',
          borderColor: '#cccccc',
          borderWidth: 1,
        }]
      };
    }
    
    return {
      labels: topProducts.map(product => product.name),
      datasets: [{
        label: 'Revenue (₹)',
        data: topProducts.map(product => product.sales?.totalRevenue || 0),
        backgroundColor: '#1890ff',
        borderColor: '#1890ff',
        borderWidth: 1,
      }]
    };
  }, [dashboardData]);

  // Memoized chart options
  const chartOptions = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          font: {
            size: isMobile ? 10 : 12
          }
        }
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          font: {
            size: isMobile ? 8 : 10
          }
        }
      },
      x: {
        ticks: {
          font: {
            size: isMobile ? 8 : 10
          }
        }
      }
    },
  }), [isMobile]);

  const horizontalBarOptions = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    indexAxis: 'y',
    plugins: {
      legend: {
        position: 'top',
        labels: {
          font: {
            size: isMobile ? 10 : 12
          }
        }
      },
    },
    scales: {
      x: {
        beginAtZero: true,
        ticks: {
          font: {
            size: isMobile ? 8 : 10
          }
        }
      },
      y: {
        ticks: {
          font: {
            size: isMobile ? 8 : 10
          }
        }
      }
    },
  }), [isMobile]);

  const pieOptions = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: isMobile ? 'bottom' : 'right',
        labels: {
          font: {
            size: isMobile ? 10 : 12
          }
        }
      },
    },
  }), [isMobile]);

  const cardHeight = isMobile ? '250px' : isTablet ? '300px' : '400px';
  const chartHeight = isMobile ? '150px' : isTablet ? '200px' : '300px';

  if (loading && !dashboardData) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>
          <span>Loading analytics...</span>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div style={{ 
        marginBottom: 24, 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'flex-start',
        flexWrap: 'wrap',
        gap: 16
      }}>
        <div style={{ flex: 1, minWidth: 200 }}>
          <AntTitle level={isMobile ? 3 : 2} style={{ margin: 0, color: '#52c41a' }}>
            Vendor Dashboard
          </AntTitle>
          <p style={{ 
            color: '#666', 
            marginTop: '8px', 
            fontSize: isMobile ? '12px' : '14px',
            margin: '4px 0'
          }}>
            Real-time insights and performance metrics for your store
          </p>
          {lastUpdated && (
            <div style={{ fontSize: '11px', color: '#999', marginTop: 4 }}>
              Last updated: {dayjs(lastUpdated).format('MMM DD, YYYY HH:mm:ss')}
            </div>
          )}
        </div>
        
        <Space>
          <Select
            value={selectedPeriod}
            onChange={handlePeriodChange}
            size={isMobile ? 'small' : 'default'}
            style={{ minWidth: 100 }}
          >
            <Select.Option value="7d">7 Days</Select.Option>
            <Select.Option value="30d">30 Days</Select.Option>
            <Select.Option value="90d">90 Days</Select.Option>
            <Select.Option value="1y">1 Year</Select.Option>
          </Select>
          
          <Button 
            icon={<ReloadOutlined />} 
            onClick={handleRefresh}
            loading={refreshing}
            size={isMobile ? 'small' : 'default'}
          >
            {!isMobile && 'Refresh'}
          </Button>
        </Space>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert
          message="Data Loading Issue"
          description={error}
          type="warning"
          icon={<WarningOutlined />}
          showIcon
          style={{ marginBottom: 16 }}
          closable
          onClose={() => setError(null)}
        />
      )}

      {/* Statistics Cards */}
      <Row gutter={[8, 8]} style={{ marginBottom: 16 }}>
        <Col xs={12} sm={12} md={6} lg={6}>
          <Card size={isMobile ? 'small' : 'default'}>
            <Statistic
              title="Total Revenue"
              value={dashboardData?.orders?.totalRevenue || 0}
              formatter={(value) => formatCurrency(value)}
              prefix={<DollarOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ 
                color: '#52c41a', 
                fontSize: isMobile ? '16px' : '24px' 
              }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6} lg={6}>
          <Card size={isMobile ? 'small' : 'default'}>
            <Statistic
              title="Total Orders"
              value={dashboardData?.orders?.totalOrders || 0}
              prefix={<ShoppingCartOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ 
                color: '#1890ff', 
                fontSize: isMobile ? '16px' : '24px' 
              }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6} lg={6}>
          <Card size={isMobile ? 'small' : 'default'}>
            <Statistic
              title="Total Products"
              value={dashboardData?.products?.totalProducts || 0}
              prefix={<ProductOutlined style={{ color: '#faad14' }} />}
              valueStyle={{ 
                color: '#faad14', 
                fontSize: isMobile ? '16px' : '24px' 
              }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6} lg={6}>
          <Card size={isMobile ? 'small' : 'default'}>
            <Statistic
              title="Avg Order Value"
              value={dashboardData?.orders?.averageOrderValue || 0}
              formatter={(value) => formatCurrency(value)}
              prefix={<EyeOutlined style={{ color: '#722ed1' }} />}
              valueStyle={{ 
                color: '#722ed1', 
                fontSize: isMobile ? '16px' : '24px' 
              }}
            />
          </Card>
        </Col>
      </Row>

      {/* Charts Row 1 */}
      <Row gutter={[8, 8]} style={{ marginBottom: 16 }}>
        <Col xs={24} lg={12}>
          <Card 
            title="Sales Performance" 
            extra={<Badge status="processing" text={isMobile ? "Live" : "Live Data"} />}
            style={{ height: cardHeight }}
            size={isMobile ? 'small' : 'default'}
          >
            <div style={{ height: chartHeight }}>
              <Line data={salesPerformanceData} options={chartOptions} />
            </div>
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card 
            title="Product Performance" 
            extra={<Badge status="success" text="Updated" />}
            style={{ height: cardHeight }}
            size={isMobile ? 'small' : 'default'}
          >
            <div style={{ height: chartHeight }}>
              <Bar data={productPerformanceData} options={chartOptions} />
            </div>
          </Card>
        </Col>
      </Row>

      {/* Charts Row 2 */}
      <Row gutter={[8, 8]} style={{ marginBottom: 16 }}>
        <Col xs={24} lg={12}>
          <Card 
            title="Revenue Breakdown" 
            extra={<Badge status="default" text="This Month" />}
            style={{ height: cardHeight }}
            size={isMobile ? 'small' : 'default'}
          >
            <div style={{ height: chartHeight }}>
              <Pie data={revenueBreakdownData} options={pieOptions} />
            </div>
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card 
            title="Top Selling Products" 
            extra={<Badge status="processing" text="Revenue Based" />}
            style={{ height: cardHeight }}
            size={isMobile ? 'small' : 'default'}
          >
            <div style={{ height: chartHeight }}>
              <Bar data={topSellingProductsData} options={horizontalBarOptions} />
            </div>
          </Card>
        </Col>
      </Row>

      {/* Store Performance & Recent Orders */}
      <Row gutter={[8, 8]}>
        <Col xs={24} lg={12}>
          <Card 
            title="Store Performance" 
            size={isMobile ? 'small' : 'default'}
          >
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <div>
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  marginBottom: '8px',
                  fontSize: isMobile ? '12px' : '14px'
                }}>
                  <span>Average Customer Rating</span>
                  <span style={{ fontWeight: 500 }}>{dashboardData?.products?.averageRating?.toFixed(1) || 'N/A'}/5.0</span>
                </div>
              </div>
              <div>
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  marginBottom: '8px',
                  fontSize: isMobile ? '12px' : '14px'
                }}>
                  <span>Total Orders Received</span>
                  <span style={{ fontWeight: 500 }}>{dashboardData?.orders?.totalOrders || 0}</span>
                </div>
              </div>
              <div>
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  marginBottom: '8px',
                  fontSize: isMobile ? '12px' : '14px'
                }}>
                  <span>Order Fulfillment Rate</span>
                  <span style={{ fontWeight: 500, color: '#52c41a' }}>
                    {(() => {
                      const totalOrders = dashboardData?.orders?.totalOrders || 0;
                      const deliveredOrders = dashboardData?.orders?.deliveredOrders || 0;
                      const rate = totalOrders > 0 ? ((deliveredOrders / totalOrders) * 100).toFixed(1) : '0.0';
                      return `${rate}%`;
                    })()}
                  </span>
                </div>
              </div>
              <div>
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  marginBottom: '8px',
                  fontSize: isMobile ? '12px' : '14px'
                }}>
                  <span>Total Revenue Generated</span>
                  <span style={{ fontWeight: 500, color: '#52c41a' }}>{formatCurrency(dashboardData?.orders?.totalRevenue || 0)}</span>
                </div>
              </div>
            </Space>
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card 
            title="Recent Orders" 
            size={isMobile ? 'small' : 'default'}
          >
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              {dashboardData?.recentOrders && dashboardData.recentOrders.length > 0 ? (
                dashboardData.recentOrders.map((order, index) => {
                  const customer = order.customer;
                  const customerName = customer ? `${customer.firstName} ${customer.lastName}` : 'Unknown Customer';
                  const initials = customer 
                    ? `${customer.firstName?.charAt(0) || ''}${customer.lastName?.charAt(0) || ''}`.toUpperCase()
                    : 'UK';
                  
                  const colors = ['#52c41a', '#1890ff', '#faad14', '#f5222d', '#722ed1'];
                  const bgColor = colors[index % colors.length];
                  
                  return (
                    <div key={order._id} style={{ display: 'flex', alignItems: 'center', gap: isMobile ? '8px' : '12px' }}>
                      <Avatar 
                        style={{ backgroundColor: bgColor }} 
                        size={isMobile ? 'small' : 'default'}
                      >
                        {initials}
                      </Avatar>
                      <div style={{ flex: 1 }}>
                        <div style={{ fontWeight: 500, fontSize: isMobile ? '12px' : '14px' }}>
                          {order.orderNumber}
                        </div>
                        <div style={{ color: '#666', fontSize: isMobile ? '10px' : '12px' }}>
                          {customerName}
                        </div>
                      </div>
                      <div style={{ textAlign: 'right' }}>
                        <div style={{ fontWeight: 500, color: '#52c41a', fontSize: isMobile ? '12px' : '14px' }}>
                          {formatCurrency(order.pricing?.total || 0)}
                        </div>
                        <div style={{ color: '#666', fontSize: isMobile ? '10px' : '12px' }}>
                          {dayjs(order.createdAt).fromNow()}
                        </div>
                      </div>
                    </div>
                  );
                })
              ) : (
                <div style={{ 
                  textAlign: 'center', 
                  color: '#999', 
                  padding: '20px 0',
                  fontSize: isMobile ? '12px' : '14px'
                }}>
                  No recent orders found
                </div>
              )}
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default ResponsiveVendorDashboard;