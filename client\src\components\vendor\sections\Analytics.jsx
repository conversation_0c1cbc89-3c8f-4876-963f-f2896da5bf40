import React, { useCallback } from 'react';
import { Spin, Alert, Row, Col } from 'antd';
import { WarningOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

// Import modular components
import AnalyticsHeader from '../analytics/AnalyticsHeader';
import StatisticsCards from '../analytics/StatisticsCards';
import ChartsSection from '../analytics/ChartsSection';
import PerformanceMetrics from '../analytics/PerformanceMetrics';
import RecentOrders from '../analytics/RecentOrders';
import useAnalyticsData from '../analytics/useAnalyticsData';
// Debug component removed - using environment-based configuration instead

// Extend dayjs with relativeTime plugin
dayjs.extend(relativeTime);

const Analytics = () => {
  const {
    loading,
    refreshing, 
    dashboardData,
    analyticsData,
    selectedPeriod,
    lastUpdated,
    error,
    handleRefresh,
    handlePeriodChange,
    setError
  } = useAnalyticsData();
  
  // Format currency
  const formatCurrency = useCallback((amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount || 0);
  }, []);
  
  if (loading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>
          <span>Loading vendor analytics...</span>
        </div>
        <div style={{ marginTop: '8px', fontSize: '12px', color: '#999' }}>
          <span>Fetching dashboard data and charts...</span>
        </div>
      </div>
    );
  }
  
  return (
    <div>
      {/* Header */}
      <AnalyticsHeader
        selectedPeriod={selectedPeriod}
        onPeriodChange={handlePeriodChange}
        onRefresh={handleRefresh}
        refreshing={refreshing}
        lastUpdated={lastUpdated}
      />
      
      {/* Error Alert */}
      {error && (
        <Alert
          message="Data Loading Issue"
          description={error}
          type="warning"
          icon={<WarningOutlined />}
          showIcon
          style={{ marginBottom: 16 }}
          closable
          onClose={() => setError(null)}
        />
      )}
      
      {/* Statistics Cards */}
      <StatisticsCards 
        dashboardData={dashboardData}
        loading={loading}
        formatCurrency={formatCurrency}
      />
      
      {/* Charts Section */}
      <ChartsSection 
        analyticsData={analyticsData}
        dashboardData={dashboardData}
        loading={loading}
      />
      
      {/* Performance Metrics & Recent Orders */}
      <Row gutter={[8, 8]}>
        <Col xs={24} lg={12}>
          <PerformanceMetrics 
            dashboardData={dashboardData}
            loading={loading}
          />
        </Col>
        <Col xs={24} lg={12}>
          <RecentOrders 
            dashboardData={dashboardData}
            loading={loading}
            formatCurrency={formatCurrency}
          />
        </Col>
      </Row>
    </div>
  );
};

export default Analytics;
