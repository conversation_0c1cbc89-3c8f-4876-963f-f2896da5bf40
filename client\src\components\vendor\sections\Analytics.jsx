import React, { useState, useEffect, useCallback } from 'react';
import {
  Layout, Card, Row, Col, Statistic, Typography, Space, Badge, message, Spin, Alert,
  Button, Select
} from 'antd';
import {
  ShopOutlined,
  ShoppingCartOutlined,
  DollarOutlined,
  RiseOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  ReloadOutlined,
  WarningOutlined
} from '@ant-design/icons';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import { dashboardApi } from '../../../services/vendorApi';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

// Add dayjs plugins
dayjs.extend(relativeTime);

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

const { Content } = Layout;
const { Title: AntTitle, Text } = Typography;
const { Option } = Select;

const Analytics = () => {
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [dashboardData, setDashboardData] = useState(null);
  const [analyticsData, setAnalyticsData] = useState(null);
  const [revenueData, setRevenueData] = useState(null);
  const [orderData, setOrderData] = useState(null);
  const [error, setError] = useState(null);
  const [selectedPeriod, setSelectedPeriod] = useState('30d');

  // Format currency
  const formatCurrency = useCallback((amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount || 0);
  }, []);

  // Fetch dashboard data
  const fetchDashboardData = async () => {
    try {
      const response = await Promise.race([
        dashboardApi.getStats(),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Request timeout')), 10000)
        )
      ]);

      if (response?.data?.success) {
        setDashboardData(response.data.data);
        setError(null);
      } else {
        throw new Error(response?.data?.message || 'Failed to fetch dashboard data');
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);

      let errorMessage = 'Failed to load dashboard data';
      if (error.response?.status === 401) {
        errorMessage = 'Session expired. Please log in again.';
      } else if (error.response?.status === 403) {
        errorMessage = 'Access denied. Please check your permissions.';
      } else if (error.message?.includes('timeout')) {
        errorMessage = 'Request timed out. Please check your connection.';
      }

      setError(errorMessage);

      // Set fallback data
      setDashboardData({
        products: { totalProducts: 0, activeProducts: 0, averageRating: 0 },
        orders: { totalOrders: 0, totalRevenue: 0, averageOrderValue: 0, deliveredOrders: 0 },
        today: { todayOrders: 0, todayRevenue: 0 },
        recentOrders: [],
        topProducts: []
      });
    }
  };

  // Fetch analytics data
  const fetchAnalyticsData = async (period = selectedPeriod) => {
    try {
      const response = await Promise.race([
        dashboardApi.getAnalytics({ period, type: 'revenue' }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Analytics request timeout')), 10000)
        )
      ]);

      if (response?.data?.success) {
        const analytics = response.data.data.analytics || [];
        setAnalyticsData(response.data.data);

        // Process revenue data for chart
        if (analytics.length > 0) {
          setRevenueData({
            labels: analytics.map(item => dayjs(item._id).format('MMM DD')),
            datasets: [
              {
                label: 'Revenue (₹)',
                data: analytics.map(item => item.revenue || 0),
                borderColor: '#1890ff',
                backgroundColor: 'rgba(24, 144, 255, 0.1)',
                fill: true,
                tension: 0.4,
              },
            ],
          });

          // Process order data for chart
          setOrderData({
            labels: analytics.map(item => dayjs(item._id).format('MMM DD')),
            datasets: [
              {
                label: 'Orders',
                data: analytics.map(item => item.orders || 0),
                borderColor: '#52c41a',
                backgroundColor: 'rgba(82, 196, 26, 0.2)',
                fill: true,
                tension: 0.4,
              },
            ],
          });
        } else {
          // Set empty chart data when no analytics data
          setRevenueData(null);
          setOrderData(null);
        }
      } else {
        setAnalyticsData({ analytics: [], period, type: 'revenue' });
        setRevenueData(null);
        setOrderData(null);
      }
    } catch (error) {
      console.error('Error fetching analytics data:', error);
      setAnalyticsData({ analytics: [], period, type: 'revenue' });
      setRevenueData(null);
      setOrderData(null);
    }
  };

  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    await Promise.all([
      fetchDashboardData(),
      fetchAnalyticsData()
    ]);
    setRefreshing(false);
    message.success('Analytics refreshed');
  };

  // Handle period change
  const handlePeriodChange = (newPeriod) => {
    setSelectedPeriod(newPeriod);
    fetchAnalyticsData(newPeriod);
  };

  // Initialize data on mount
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([
        fetchDashboardData(),
        fetchAnalyticsData()
      ]);
      setLoading(false);
    };

    loadData();
  }, []);

  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  // Loading state
  if (loading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>
          <span>Loading vendor analytics...</span>
        </div>
      </div>
    );
  }
  
  return (
    <Layout style={{ minHeight: '100vh', background: '#f0f2f5' }}>
      <Content style={{ padding: '24px' }}>
        {/* Header */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
          <div>
            <AntTitle level={2} style={{ margin: 0, color: '#1890ff' }}>
              Vendor Analytics
            </AntTitle>
            <p style={{ color: '#666', marginTop: '8px' }}>
              Track your store performance and sales metrics
            </p>
          </div>
          <Space>
            <Select
              value={selectedPeriod}
              onChange={handlePeriodChange}
              style={{ width: 120 }}
            >
              <Option value="7d">Last 7 days</Option>
              <Option value="30d">Last 30 days</Option>
              <Option value="90d">Last 90 days</Option>
            </Select>
            <ReloadOutlined
              onClick={handleRefresh}
              spin={refreshing}
              style={{ cursor: 'pointer', color: '#1890ff' }}
            />
          </Space>
        </div>

        {/* Error Alert */}
        {error && (
          <Alert
            message="Data Loading Issue"
            description={error}
            type="warning"
            icon={<WarningOutlined />}
            showIcon
            style={{ marginBottom: 16 }}
            closable
            onClose={() => setError(null)}
          />
        )}

        {/* Statistics Cards */}
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Total Products"
                value={dashboardData?.products?.totalProducts || 0}
                prefix={<ShopOutlined style={{ color: '#1890ff' }} />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Active Products"
                value={dashboardData?.products?.activeProducts || 0}
                prefix={<RiseOutlined style={{ color: '#52c41a' }} />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Total Orders"
                value={dashboardData?.orders?.totalOrders || 0}
                prefix={<ShoppingCartOutlined style={{ color: '#faad14' }} />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Total Revenue"
                value={formatCurrency(dashboardData?.orders?.totalRevenue || 0)}
                prefix={<DollarOutlined style={{ color: '#f5222d' }} />}
                valueStyle={{ color: '#f5222d' }}
              />
            </Card>
          </Col>
        </Row>

        {/* Charts Row */}
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={24} lg={12}>
            <Card
              title="Revenue Trends"
              extra={<Badge status="processing" text="Live Data" />}
              style={{ height: '400px' }}
            >
              <div style={{ height: '300px' }}>
                {revenueData ? (
                  <Line data={revenueData} options={chartOptions} />
                ) : (
                  <div style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                    {loading ? (
                      <Spin tip="Loading revenue data..." />
                    ) : (
                      <>
                        <Text type="secondary">No revenue data available</Text>
                        <Text type="secondary" style={{ fontSize: '12px', marginTop: '8px' }}>
                          Data will appear once you have orders
                        </Text>
                      </>
                    )}
                  </div>
                )}
              </div>
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card
              title="Order Trends"
              extra={<Badge status="success" text="Updated" />}
              style={{ height: '400px' }}
            >
              <div style={{ height: '300px' }}>
                {orderData ? (
                  <Line data={orderData} options={chartOptions} />
                ) : (
                  <div style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                    {loading ? (
                      <Spin tip="Loading order data..." />
                    ) : (
                      <>
                        <Text type="secondary">No order data available</Text>
                        <Text type="secondary" style={{ fontSize: '12px', marginTop: '8px' }}>
                          Data will appear once you have orders
                        </Text>
                      </>
                    )}
                  </div>
                )}
              </div>
            </Card>
          </Col>
        </Row>

        {/* Today's Stats */}
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={12}>
            <Card title="Today's Performance">
              <Row gutter={16}>
                <Col span={12}>
                  <Statistic
                    title="Today's Orders"
                    value={dashboardData?.today?.todayOrders || 0}
                    prefix={<ShoppingCartOutlined />}
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title="Today's Revenue"
                    value={formatCurrency(dashboardData?.today?.todayRevenue || 0)}
                    prefix={<DollarOutlined />}
                  />
                </Col>
              </Row>
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card title="Quick Stats">
              <Row gutter={16}>
                <Col span={12}>
                  <Statistic
                    title="Average Rating"
                    value={dashboardData?.products?.averageRating || 0}
                    precision={1}
                    suffix="/ 5"
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title="Avg Order Value"
                    value={formatCurrency(dashboardData?.orders?.averageOrderValue || 0)}
                  />
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
      </Content>
    </Layout>
  );
};

export default Analytics;
