@echo off
echo ========================================
echo COMPLETE EPERM ERROR FIX FOR VITE
echo ========================================
echo.
echo This script will:
echo 1. Stop all Node.js processes
echo 2. Remove problematic dependencies
echo 3. Clean all caches
echo 4. Reinstall dependencies
echo 5. Configure Vite for Windows
echo.

pause

echo Step 1: Stopping all Node.js processes...
taskkill /f /im node.exe 2>nul
taskkill /f /im "vite.exe" 2>nul
timeout /t 2 /nobreak >nul

echo Step 2: Removing problematic cache directories...
if exist "node_modules\.vite" (
    echo Removing .vite cache...
    rmdir /s /q "node_modules\.vite" 2>nul
)

if exist ".vite-cache" (
    echo Removing .vite-cache...
    rmdir /s /q ".vite-cache" 2>nul
)

if exist "node_modules\.cache" (
    echo Removing .cache...
    rmdir /s /q "node_modules\.cache" 2>nul
)

echo Step 3: Removing socket.io-client (causing EPERM issues)...
npm uninstall socket.io-client 2>nul

echo Step 4: Clearing all caches...
npm cache clean --force
npm cache verify

echo Step 5: Removing node_modules and package-lock.json...
if exist "node_modules" (
    echo Removing node_modules...
    rmdir /s /q "node_modules"
)
if exist "package-lock.json" (
    echo Removing package-lock.json...
    del "package-lock.json"
)

echo Step 6: Fresh installation without problematic packages...
npm install

echo Step 7: Setting up Vite prevention scripts...
node prevent-vite-errors.js

echo Step 8: Testing development server...
echo Starting development server in 3 seconds...
timeout /t 3 /nobreak >nul

echo ========================================
echo EPERM ERROR FIX COMPLETED!
echo ========================================
echo.
echo The following changes were made:
echo - Removed socket.io-client (was causing EPERM errors)
echo - Cleaned all Vite caches
echo - Reinstalled dependencies
echo - Configured Vite for Windows
echo - Added prevention scripts
echo.
echo To start development server:
echo   npm run dev
echo.
echo If EPERM errors occur again:
echo   run quick-fix-eperm.bat
echo.

npm run dev

pause