import React from 'react';
import { 
  FacebookOutlined, 
  TwitterOutlined, 
  InstagramOutlined, 
  LinkedinOutlined,
  YoutubeOutlined,
  MailOutlined,
  PhoneOutlined,
  EnvironmentOutlined
} from '@ant-design/icons';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const footerSections = [
    {
      title: "Customer Service",
      links: [
        { name: "Help Center", href: "/help" },
        { name: "Contact Us", href: "/contact" },
        { name: "Report Abuse", href: "/report" },
        { name: "Submit a Dispute", href: "/dispute" },
        { name: "Policies & Rules", href: "/policies" },
        { name: "Online Shopping", href: "/shopping-guide" }
      ]
    },
    {
      title: "Trade Services",
      links: [
        { name: "Trade Assurance", href: "/trade-assurance" },
        { name: "Business Identity", href: "/business-identity" },
        { name: "Logistics Service", href: "/logistics" },
        { name: "Production Monitoring", href: "/monitoring" },
        { name: "Inspection Service", href: "/inspection" },
        { name: "Letter of Credit", href: "/credit" }
      ]
    },
    {
      title: "Sell on Our Platform",
      links: [
        { name: "Start Selling", href: "/start-selling" },
        { name: "Seller Central", href: "/seller-central" },
        { name: "Become a Supplier", href: "/become-supplier" },
        { name: "Seller Membership", href: "/membership" },
        { name: "Learning Center", href: "/learning" },
        { name: "Partner Program", href: "/partners" }
      ]
    },
    {
      title: "About Us",
      links: [
        { name: "About Our Company", href: "/about" },
        { name: "Company News", href: "/news" },
        { name: "Investor Relations", href: "/investors" },
        { name: "Careers", href: "/careers" },
        { name: "Global Sites", href: "/global" },
        { name: "Sustainability", href: "/sustainability" }
      ]
    }
  ];


  return (
    <footer className="bg-gray-900 text-white">
      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {footerSections.map((section, index) => (
            <div key={index}>
              <h4 className="text-lg font-semibold mb-4 text-white">{section.title}</h4>
              <ul className="space-y-2">
                {section.links.map((link, linkIndex) => (
                  <li key={linkIndex}>
                    <a
                      href={link.href}
                      className="text-gray-300 hover:text-white transition-colors duration-200 text-sm"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>


        {/* Contact Info & Apps */}
        <div className="mt-12 pt-8 border-t border-gray-700">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Contact Information */}
            <div>
              <h4 className="text-lg font-semibold mb-4">Contact Information</h4>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <PhoneOutlined className="text-blue-400" />
                  <span className="text-gray-300">+****************</span>
                </div>
                <div className="flex items-center gap-3">
                  <MailOutlined className="text-blue-400" />
                  <span className="text-gray-300"><EMAIL></span>
                </div>
                <div className="flex items-center gap-3">
                  <EnvironmentOutlined className="text-blue-400" />
                  <span className="text-gray-300">123 Business Ave, Suite 100, City, State 12345</span>
                </div>
              </div>
            </div>


            {/* Social Media */}
            <div>
              <h4 className="text-lg font-semibold mb-4">Follow Us</h4>
              <div className="flex gap-4">
                <a
                  href="#"
                  className="bg-blue-600 hover:bg-blue-700 p-3 rounded-full transition-colors duration-200"
                >
                  <FacebookOutlined className="text-xl" />
                </a>
                <a
                  href="#"
                  className="bg-blue-400 hover:bg-blue-500 p-3 rounded-full transition-colors duration-200"
                >
                  <TwitterOutlined className="text-xl" />
                </a>
                <a
                  href="#"
                  className="bg-pink-600 hover:bg-pink-700 p-3 rounded-full transition-colors duration-200"
                >
                  <InstagramOutlined className="text-xl" />
                </a>
                <a
                  href="#"
                  className="bg-blue-700 hover:bg-blue-800 p-3 rounded-full transition-colors duration-200"
                >
                  <LinkedinOutlined className="text-xl" />
                </a>
                <a
                  href="#"
                  className="bg-red-600 hover:bg-red-700 p-3 rounded-full transition-colors duration-200"
                >
                  <YoutubeOutlined className="text-xl" />
                </a>
              </div>
              
              {/* Trust Badges */}
              <div className="mt-6">
                <h5 className="text-sm font-semibold mb-3">Secure Shopping</h5>
                <div className="flex gap-2">
                  <div className="bg-gray-800 px-3 py-1 rounded text-xs">SSL Secured</div>
                  <div className="bg-gray-800 px-3 py-1 rounded text-xs">PCI Compliant</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="bg-gray-950 py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex flex-col md:flex-row items-center gap-4 mb-4 md:mb-0">
              <div className="text-sm text-gray-400">
                © {currentYear} Marketplace. All rights reserved.
              </div>
              <div className="flex gap-4 text-sm">
                <a href="/privacy" className="text-gray-400 hover:text-white transition-colors duration-200">
                  Privacy Policy
                </a>
                <a href="/terms" className="text-gray-400 hover:text-white transition-colors duration-200">
                  Terms of Service
                </a>
                <a href="/cookies" className="text-gray-400 hover:text-white transition-colors duration-200">
                  Cookie Policy
                </a>
                <a href="/sitemap" className="text-gray-400 hover:text-white transition-colors duration-200">
                  Sitemap
                </a>
              </div>
            </div>
            
            {/* Payment Methods */}
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-400 mr-2">We Accept:</span>
              <div className="flex gap-2">
                <div className="bg-white p-1 rounded text-xs text-gray-900 font-semibold">VISA</div>
                <div className="bg-white p-1 rounded text-xs text-gray-900 font-semibold">MC</div>
                <div className="bg-white p-1 rounded text-xs text-gray-900 font-semibold">AMEX</div>
                <div className="bg-blue-600 p-1 rounded text-xs text-white font-semibold">PayPal</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;