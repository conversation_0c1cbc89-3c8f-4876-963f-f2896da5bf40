const express = require('express');
const router = express.Router();
const { verifyToken, requireUserType } = require('../../middleware/auth/authMiddleware');
const {
  getAnalytics
} = require('../../controllers/vendor/dashboardController');

// Apply authentication middleware to all routes
router.use(verifyToken);
router.use(requireUserType(['vendor']));

/**
 * @route   GET /api/vendor/analytics
 * @desc    Get vendor analytics data
 * @access  Private (Vendor)
 */
router.get('/', getAnalytics);

module.exports = router;
