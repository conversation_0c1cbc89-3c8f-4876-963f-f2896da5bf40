import React from 'react';
import { Layout, Card, Row, Col, Statistic, Typography, Space, Avatar, Badge, Progress } from 'antd';
import {
  ShoppingCartOutlined,
  DollarOutlined,
  ProductOutlined,
  EyeOutlined,
  RiseOutlined,
  StarOutlined
} from '@ant-design/icons';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { Line, Bar, Pie } from 'react-chartjs-2';

const { Content } = Layout;
const { Title: AntTitle } = Typography;

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

const VendorDashboard = () => {
  // Sample data for charts
  const salesPerformanceData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    datasets: [
      {
        label: 'Sales ($)',
        data: [3200, 4100, 3800, 5200, 4800, 6100, 5900, 7200, 6800, 8100, 7700, 9200],
        borderColor: '#52c41a',
        backgroundColor: 'rgba(82, 196, 26, 0.1)',
        fill: true,
        tension: 0.4,
      },
    ],
  };

  const productPerformanceData = {
    labels: ['Laptop Pro', 'Wireless Mouse', 'Keyboard', 'Monitor', 'Headphones', 'Webcam'],
    datasets: [
      {
        label: 'Units Sold',
        data: [45, 78, 62, 34, 89, 23],
        backgroundColor: [
          '#1890ff',
          '#52c41a',
          '#faad14',
          '#f5222d',
          '#722ed1',
          '#13c2c2',
        ],
      },
    ],
  };

  const revenueBreakdownData = {
    labels: ['Product Sales', 'Shipping', 'Commission', 'Returns'],
    datasets: [
      {
        data: [75, 15, 8, 2],
        backgroundColor: [
          '#52c41a',
          '#1890ff',
          '#faad14',
          '#f5222d',
        ],
      },
    ],
  };

  const topSellingProductsData = {
    labels: ['Laptop Pro', 'Wireless Mouse', 'Gaming Keyboard', 'USB-C Hub', '4K Monitor'],
    datasets: [
      {
        label: 'Revenue ($)',
        data: [12500, 8900, 7200, 5800, 4300],
        backgroundColor: '#1890ff',
        borderColor: '#1890ff',
        borderWidth: 1,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  const horizontalBarOptions = {
    responsive: true,
    maintainAspectRatio: false,
    indexAxis: 'y',
    plugins: {
      legend: {
        position: 'top',
      },
    },
    scales: {
      x: {
        beginAtZero: true,
      },
    },
  };

  const pieOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
      },
    },
  };

  return (
    <Layout style={{ minHeight: '100vh', background: '#f0f2f5' }}>
      <Content style={{ padding: '24px' }}>
        <div style={{ marginBottom: '24px' }}>
          <AntTitle level={2} style={{ margin: 0, color: '#52c41a' }}>
            Vendor Dashboard
          </AntTitle>
          <p style={{ color: '#666', marginTop: '8px' }}>
            Track your store performance and manage your business effectively.
          </p>
        </div>

        {/* Statistics Cards */}
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Total Sales"
                value={68420}
                prefix={<DollarOutlined style={{ color: '#52c41a' }} />}
                precision={2}
                suffix={
                  <Badge count={<RiseOutlined style={{ color: '#52c41a' }} />} />
                }
                valueStyle={{ color: '#52c41a' }}
              />
              <Progress percent={85} size="small" strokeColor="#52c41a" style={{ marginTop: '8px' }} />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Orders"
                value={342}
                prefix={<ShoppingCartOutlined style={{ color: '#1890ff' }} />}
                suffix={
                  <Badge count={<RiseOutlined style={{ color: '#52c41a' }} />} />
                }
                valueStyle={{ color: '#1890ff' }}
              />
              <Progress percent={72} size="small" strokeColor="#1890ff" style={{ marginTop: '8px' }} />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Products"
                value={28}
                prefix={<ProductOutlined style={{ color: '#faad14' }} />}
                valueStyle={{ color: '#faad14' }}
              />
              <Progress percent={60} size="small" strokeColor="#faad14" style={{ marginTop: '8px' }} />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Store Views"
                value={15680}
                prefix={<EyeOutlined style={{ color: '#722ed1' }} />}
                suffix={
                  <Badge count={<RiseOutlined style={{ color: '#52c41a' }} />} />
                }
                valueStyle={{ color: '#722ed1' }}
              />
              <Progress percent={90} size="small" strokeColor="#722ed1" style={{ marginTop: '8px' }} />
            </Card>
          </Col>
        </Row>

        {/* Charts Row 1 */}
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={24} lg={12}>
            <Card 
              title="Sales Performance" 
              extra={<Badge status="processing" text="Live Data" />}
              style={{ height: '400px' }}
            >
              <div style={{ height: '300px' }}>
                <Line data={salesPerformanceData} options={chartOptions} />
              </div>
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card 
              title="Product Performance" 
              extra={<Badge status="success" text="Updated" />}
              style={{ height: '400px' }}
            >
              <div style={{ height: '300px' }}>
                <Bar data={productPerformanceData} options={chartOptions} />
              </div>
            </Card>
          </Col>
        </Row>

        {/* Charts Row 2 */}
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={12}>
            <Card 
              title="Revenue Breakdown" 
              extra={<Badge status="default" text="This Month" />}
              style={{ height: '400px' }}
            >
              <div style={{ height: '300px' }}>
                <Pie data={revenueBreakdownData} options={pieOptions} />
              </div>
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card 
              title="Top Selling Products" 
              extra={<Badge status="processing" text="Revenue Based" />}
              style={{ height: '400px' }}
            >
              <div style={{ height: '300px' }}>
                <Bar data={topSellingProductsData} options={horizontalBarOptions} />
              </div>
            </Card>
          </Col>
        </Row>

        {/* Store Performance & Recent Orders */}
        <Row gutter={[16, 16]} style={{ marginTop: '24px' }}>
          <Col xs={24} lg={12}>
            <Card title="Store Performance" extra={<a href="#">View Details</a>}>
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                <div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                    <span>Customer Rating</span>
                    <span style={{ fontWeight: 500 }}>4.8/5.0</span>
                  </div>
                  <Progress percent={96} strokeColor="#faad14" />
                </div>
                <div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                    <span>Order Fulfillment</span>
                    <span style={{ fontWeight: 500 }}>98%</span>
                  </div>
                  <Progress percent={98} strokeColor="#52c41a" />
                </div>
                <div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                    <span>Response Time</span>
                    <span style={{ fontWeight: 500 }}>2.3 hrs</span>
                  </div>
                  <Progress percent={85} strokeColor="#1890ff" />
                </div>
                <div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                    <span>Return Rate</span>
                    <span style={{ fontWeight: 500 }}>2.1%</span>
                  </div>
                  <Progress percent={15} strokeColor="#f5222d" />
                </div>
              </Space>
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card title="Recent Orders" extra={<a href="#">View All</a>}>
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <Avatar style={{ backgroundColor: '#52c41a' }}>JD</Avatar>
                  <div style={{ flex: 1 }}>
                    <div style={{ fontWeight: 500 }}>Order #12345</div>
                    <div style={{ color: '#666', fontSize: '12px' }}>John Doe - Laptop Pro</div>
                  </div>
                  <div style={{ textAlign: 'right' }}>
                    <div style={{ fontWeight: 500, color: '#52c41a' }}>$1,299</div>
                    <div style={{ color: '#666', fontSize: '12px' }}>2 min ago</div>
                  </div>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <Avatar style={{ backgroundColor: '#1890ff' }}>SM</Avatar>
                  <div style={{ flex: 1 }}>
                    <div style={{ fontWeight: 500 }}>Order #12344</div>
                    <div style={{ color: '#666', fontSize: '12px' }}>Sarah Miller - Wireless Mouse</div>
                  </div>
                  <div style={{ textAlign: 'right' }}>
                    <div style={{ fontWeight: 500, color: '#52c41a' }}>$89</div>
                    <div style={{ color: '#666', fontSize: '12px' }}>15 min ago</div>
                  </div>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <Avatar style={{ backgroundColor: '#faad14' }}>MJ</Avatar>
                  <div style={{ flex: 1 }}>
                    <div style={{ fontWeight: 500 }}>Order #12343</div>
                    <div style={{ color: '#666', fontSize: '12px' }}>Mike Johnson - Gaming Keyboard</div>
                  </div>
                  <div style={{ textAlign: 'right' }}>
                    <div style={{ fontWeight: 500, color: '#52c41a' }}>$159</div>
                    <div style={{ color: '#666', fontSize: '12px' }}>1 hour ago</div>
                  </div>
                </div>
              </Space>
            </Card>
          </Col>
        </Row>
      </Content>
    </Layout>
  );
};

export default VendorDashboard;