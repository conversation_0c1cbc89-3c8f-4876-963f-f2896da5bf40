const fs = require('fs');
const path = require('path');

// Function to recursively find all JSX files
function findJSXFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !file.includes('node_modules') && !file.includes('.git')) {
      findJSXFiles(filePath, fileList);
    } else if (file.endsWith('.jsx') || file.endsWith('.js')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Function to check for common JSX syntax errors
function checkJSXSyntax(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    const errors = [];
    
    lines.forEach((line, index) => {
      const lineNumber = index + 1;
      
      // Check for stray > characters
      if (line.match(/}}>[\s]*>[\s]*</)) {
        errors.push({
          line: lineNumber,
          error: 'Potential stray ">" character in JSX',
          content: line.trim()
        });
      }
      
      // Check for unclosed JSX tags
      if (line.match(/<[A-Z][a-zA-Z]*[^>]*[^/]>[\s]*$/)) {
        const nextLine = lines[index + 1];
        if (nextLine && nextLine.trim().startsWith('>')) {
          errors.push({
            line: lineNumber + 1,
            error: 'Potential unclosed JSX tag or stray ">"',
            content: nextLine.trim()
          });
        }
      }
      
      // Check for malformed style props
      if (line.includes('style={{') && !line.includes('}}')) {
        let found = false;
        for (let i = index + 1; i < lines.length && i < index + 5; i++) {
          if (lines[i].includes('}}')) {
            found = true;
            break;
          }
        }
        if (!found) {
          errors.push({
            line: lineNumber,
            error: 'Potentially unclosed style prop',
            content: line.trim()
          });
        }
      }
    });
    
    return errors;
  } catch (error) {
    return [{
      line: 0,
      error: `Failed to read file: ${error.message}`,
      content: ''
    }];
  }
}

// Main execution
console.log('🔍 Checking JSX syntax in all files...\n');

const srcDir = path.join(__dirname, 'src');
const jsxFiles = findJSXFiles(srcDir);

let totalErrors = 0;
let filesWithErrors = 0;

jsxFiles.forEach(filePath => {
  const errors = checkJSXSyntax(filePath);
  
  if (errors.length > 0) {
    filesWithErrors++;
    totalErrors += errors.length;
    
    console.log(`❌ ${path.relative(__dirname, filePath)}`);
    errors.forEach(error => {
      console.log(`   Line ${error.line}: ${error.error}`);
      if (error.content) {
        console.log(`   Content: ${error.content}`);
      }
    });
    console.log('');
  }
});

if (totalErrors === 0) {
  console.log('✅ No JSX syntax errors found!');
} else {
  console.log(`❌ Found ${totalErrors} potential syntax errors in ${filesWithErrors} files.`);
  console.log('\n💡 Please review and fix the errors above.');
}

console.log(`\n📊 Checked ${jsxFiles.length} files total.`);