import { useState, useEffect } from 'react';
import { notification } from 'antd';
import { dashboardApi, ordersApi, usersApi, vendorsApi, productsApi } from '../../../services/adminApi';

const useDashboardData = () => {
  // State management
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [dashboardData, setDashboardData] = useState(null);
  const [realTimeData, setRealTimeData] = useState(null);
  const [recentOrders, setRecentOrders] = useState([]);
  const [recentUsers, setRecentUsers] = useState([]);
  const [pendingApprovals, setPendingApprovals] = useState([]);
  const [vendorStats, setVendorStats] = useState(null);
  const [productStats, setProductStats] = useState(null);
  const [alerts, setAlerts] = useState([]);
  const [analyticsData, setAnalyticsData] = useState(null);
  const [selectedPeriod, setSelectedPeriod] = useState('30d');
  const [error, setError] = useState(null);

  // Initialize dashboard on component mount
  useEffect(() => {
    initializeDashboard();
  }, []);

  const initializeDashboard = async () => {
    try {
      setLoading(true);
      setError(null);

      await Promise.all([
        fetchDashboardData(),
        fetchRealTimeData(),
        fetchAnalyticsData(selectedPeriod)
      ]);
    } catch (error) {
      console.error('Error initializing dashboard:', error);
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const fetchDashboardData = async () => {
    try {
      // Try to fetch real data first
      const [
        statsResponse,
        ordersResponse,
        usersResponse,
        vendorStatsResponse,
        productStatsResponse,
        pendingProductsResponse,
        pendingVendorsResponse
      ] = await Promise.all([
        dashboardApi.getStats(),
        ordersApi.getOrders({ limit: 5, sortBy: 'createdAt', sortOrder: 'desc' }),
        usersApi.getUsers({ limit: 5, sortBy: 'createdAt', sortOrder: 'desc' }),
        vendorsApi.getStatistics(),
        productsApi.getStats(),
        productsApi.getPendingApproval({ limit: 5 }),
        vendorsApi.getPendingVerification({ limit: 5 })
      ]);

      setDashboardData(statsResponse.data.data);
      setRecentOrders(ordersResponse.data.data.orders || []);
      setRecentUsers(usersResponse.data.data.users || []);
      setVendorStats(vendorStatsResponse.data.data);
      setProductStats(productStatsResponse.data.data);

      // Combine pending approvals
      const pendingItems = [
        ...(pendingProductsResponse.data.data.products || []).map(p => ({
          type: 'product',
          id: p._id,
          name: p.name,
          vendor: p.vendor?.businessName,
          submittedAt: p.approval?.submittedAt
        })),
        ...(pendingVendorsResponse.data.data.vendors || []).map(v => ({
          type: 'vendor',
          id: v._id,
          name: v.businessName,
          email: v.user?.email,
          submittedAt: v.createdAt
        }))
      ];
      setPendingApprovals(pendingItems);

      // Set up alerts based on dashboard data
      const newAlerts = [];
      const stats = statsResponse.data.data;
      
      if (stats.alerts?.lowStockProducts > 0) {
        newAlerts.push({
          type: 'warning',
          message: `${stats.alerts.lowStockProducts} products are low in stock`,
          action: 'View Products'
        });
      }
      
      if (stats.alerts?.pendingVendors > 10) {
        newAlerts.push({
          type: 'info',
          message: `${stats.alerts.pendingVendors} vendor applications pending review`,
          action: 'Review Vendors'
        });
      }
      
      if (stats.alerts?.pendingOrders > 20) {
        newAlerts.push({
          type: 'warning',
          message: `${stats.alerts.pendingOrders} orders pending processing`,
          action: 'View Orders'
        });
      }

      setAlerts(newAlerts);
      
    } catch (apiError) {
      console.log('API not available, using mock data for demo');
      
      // Use mock data when API is not available
      setDashboardData({
        overview: {
          orders: {
            totalOrders: 1250,
            totalRevenue: 125000,
            pendingOrders: 15,
            confirmedOrders: 45,
            processingOrders: 30,
            shippedOrders: 25,
            deliveredOrders: 1100,
            cancelledOrders: 35
          },
          users: {
            totalUsers: 5420,
            activeUsers: 4890,
            inactiveUsers: 530
          },
          vendors: {
            totalVendors: 125,
            activeVendors: 98,
            pendingVendors: 12
          }
        }
      });

      setRecentOrders([
        {
          _id: '1',
          orderNumber: 'ORD240101001',
          customer: { firstName: 'John', lastName: 'Doe' },
          status: 'delivered',
          pricing: { total: 299.99 },
          createdAt: new Date().toISOString()
        },
        {
          _id: '2',
          orderNumber: 'ORD240101002',
          customer: { firstName: 'Jane', lastName: 'Smith' },
          status: 'processing',
          pricing: { total: 149.50 },
          createdAt: new Date(Date.now() - 86400000).toISOString()
        }
      ]);

      setRecentUsers([
        {
          _id: '1',
          firstName: 'Alice',
          lastName: 'Johnson',
          email: '<EMAIL>',
          role: 'customer',
          status: 'active',
          createdAt: new Date().toISOString()
        },
        {
          _id: '2',
          firstName: 'Bob',
          lastName: 'Wilson',
          email: '<EMAIL>',
          role: 'vendor',
          status: 'active',
          createdAt: new Date(Date.now() - 86400000).toISOString()
        }
      ]);

      setVendorStats({ 
        totalVendors: 125, 
        activeVendors: 98,
        totalPendingCommission: 15000.50,
        pendingVendors: 12
      });
      setProductStats({ 
        totalProducts: 850, 
        activeProducts: 780, 
        lowStockProducts: 8,
        pendingProducts: 15
      });
      setPendingApprovals([]);
      setAlerts([]);
    }
  };

  const fetchRealTimeData = async () => {
    try {
      const response = await dashboardApi.getRealTimeMetrics();
      setRealTimeData(response.data.data);
    } catch (error) {
      console.error('Error fetching real-time data:', error);
      // Mock real-time data
      setRealTimeData({
        timestamp: new Date().toISOString(),
        today: {
          orders: 45,
          revenue: 8750.50,
          newUsers: 32,
          activeUsers: 450
        }
      });
    }
  };

  const fetchAnalyticsData = async (period) => {
    try {
      const response = await dashboardApi.getAnalytics({ period, type: 'revenue' });
      setAnalyticsData(response.data.data);
    } catch (error) {
      console.error('Error fetching analytics:', error);
      // Mock analytics data
      const mockData = [
        { _id: '2024-01-01', revenue: 5000, orders: 25 },
        { _id: '2024-01-02', revenue: 7500, orders: 35 },
        { _id: '2024-01-03', revenue: 6200, orders: 28 },
        { _id: '2024-01-04', revenue: 8900, orders: 42 },
        { _id: '2024-01-05', revenue: 12000, orders: 55 }
      ];
      setAnalyticsData({ analytics: mockData });
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await initializeDashboard();
    setRefreshing(false);
    notification.success({
      message: 'Dashboard Refreshed',
      description: 'All data has been updated successfully.',
      placement: 'topRight'
    });
  };

  const handlePeriodChange = (period) => {
    setSelectedPeriod(period);
    fetchAnalyticsData(period);
  };

  return {
    // State
    loading,
    refreshing,
    dashboardData,
    realTimeData,
    recentOrders,
    recentUsers,
    pendingApprovals,
    vendorStats,
    productStats,
    alerts,
    analyticsData,
    selectedPeriod,
    error,
    
    // Actions
    handleRefresh,
    handlePeriodChange,
    initializeDashboard
  };
};

export default useDashboardData;
