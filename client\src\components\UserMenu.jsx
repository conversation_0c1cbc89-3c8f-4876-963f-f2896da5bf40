import React, { useState } from "react";
import { UserOutlined, LogoutOutlined, SettingOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../hooks/useAuth";
import { message } from "antd";

const UserMenu = ({ isMobile = false, onMenuClose }) => {
    const [isUserModalOpen, setIsUserModalOpen] = useState(false);
    const navigate = useNavigate();
    const { user, isAuthenticated, logout, userType } = useAuth();

    const getUserDisplayName = () => {
        if (!user) return 'Guest';
        if (userType === 'vendor') {
            return user.businessName || user.contactPerson || 'Vendor';
        }
        
        if (user.fullName) {
            return user.fullName;
        }
        
        const firstName = user.firstName || '';
        const lastName = user.lastName || '';
        const fullName = `${firstName} ${lastName}`.trim();
        
        return fullName || user.email || 'User';
    };

    const getUserAvatar = () => {
        if (user?.avatar) {
            return user.avatar;
        }
        return null;
    };

    const handleProfileClick = () => {
        if (isAuthenticated) {
            navigate('/profile');
        } else {
            navigate('/auth');
        }
        setIsUserModalOpen(false);
        onMenuClose?.();
    };

    const handleLogout = async () => {
        try {
            await logout();
            message.success('Logged out successfully');
            navigate('/');
        } catch (error) {
            message.error('Logout failed');
        }
        setIsUserModalOpen(false);
        onMenuClose?.();
    };

    const buttonClasses = isMobile 
        ? "flex items-center text-white hover:text-orange-500 p-2 rounded-md active:bg-gray-700 transition-colors touch-manipulation"
        : "flex items-center space-x-2 text-white hover:text-orange-500 text-sm";

    const buttonStyle = isMobile ? { minHeight: '44px', minWidth: '44px' } : {};

    const menuItemClasses = isMobile 
        ? "w-full text-left flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 active:bg-gray-200 touch-manipulation"
        : "w-full text-left flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100";

    return (
        <div className="relative">
            <button 
                onClick={() => {
                    setIsUserModalOpen(!isUserModalOpen);
                    if (isMobile && onMenuClose) {
                        onMenuClose();
                    }
                }}
                className={buttonClasses}
                style={buttonStyle}
            >
                {getUserAvatar() ? (
                    <img 
                        src={getUserAvatar()} 
                        alt="Profile" 
                        className={`rounded-full object-cover border ${isMobile ? 'w-6 h-6 border-gray-600' : 'w-8 h-8 border-2 border-gray-600'}`}
                    />
                ) : (
                    <div className={`bg-gray-600 rounded-full flex items-center justify-center ${isMobile ? 'w-6 h-6' : 'w-8 h-8'}`}>
                        <UserOutlined className={`text-white ${isMobile ? 'text-lg' : 'text-sm'}`} />
                    </div>
                )}
                {isAuthenticated && !isMobile && (
                    <span className="hidden xl:block max-w-24 truncate">
                        {getUserDisplayName()}
                    </span>
                )}
            </button>
            
            {/* User Modal */}
            {isUserModalOpen && (
                <div className="absolute top-full right-0 mt-2 w-64 bg-white border border-gray-300 rounded-md shadow-lg z-50">
                    {isAuthenticated ? (
                        <div className="py-2">
                            {/* User Info Header */}
                            <div className="px-4 py-3 border-b border-gray-200">
                                <div className="flex items-center space-x-3">
                                    {getUserAvatar() ? (
                                        <img 
                                            src={getUserAvatar()} 
                                            alt="Profile" 
                                            className="w-10 h-10 rounded-full object-cover"
                                        />
                                    ) : (
                                        <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                                            <UserOutlined className="text-gray-500" />
                                        </div>
                                    )}
                                    <div className="flex-1 min-w-0">
                                        <p className="text-sm font-medium text-gray-900 truncate">
                                            {getUserDisplayName()}
                                        </p>
                                        <p className="text-xs text-gray-500 truncate">
                                            {user.email}
                                        </p>
                                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800 mt-1">
                                            {userType === 'vendor' ? 'Vendor' : 'Customer'}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            
                            {/* Menu Items */}
                            <button 
                                onClick={handleProfileClick}
                                className={menuItemClasses}
                            >
                                <UserOutlined className="mr-3" />
                                My Profile
                            </button>
                            <button 
                                onClick={() => {
                                    navigate('/settings');
                                    setIsUserModalOpen(false);
                                    onMenuClose?.();
                                }}
                                className={menuItemClasses}
                            >
                                <SettingOutlined className="mr-3" />
                                Account Settings
                            </button>
                            
                            <hr className="my-1" />
                            <button 
                                onClick={handleLogout}
                                className={`${menuItemClasses.replace('text-gray-700 hover:bg-gray-100', 'text-red-600 hover:bg-red-50')} ${isMobile ? 'active:bg-red-100' : ''}`}
                            >
                                <LogoutOutlined className="mr-3" />
                                Sign Out
                            </button>
                        </div>
                    ) : (
                        <div className="py-2">
                            <div className="px-4 py-3 border-b border-gray-200">
                                <p className="text-sm text-gray-600">Welcome to Alicartify</p>
                            </div>
                            <button 
                                onClick={() => {
                                    navigate('/auth');
                                    setIsUserModalOpen(false);
                                    onMenuClose?.();
                                }}
                                className={menuItemClasses}
                            >
                                Sign In / Sign Up
                            </button>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

export default UserMenu;
