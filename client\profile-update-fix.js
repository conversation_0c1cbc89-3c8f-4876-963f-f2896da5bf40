// Frontend fix for profile update content-type issue
// This file can be imported in components that need to update profiles

// Enhanced fetch wrapper that ensures correct content-type
export const updateProfileWithCorrectHeaders = async (url, data, token) => {
    console.log('🔧 Profile update with correct headers');
    console.log('URL:', url);
    console.log('Data:', data);
    console.log('Token exists:', !!token);

    try {
        const response = await fetch(url, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`,
                'Accept': 'application/json'
            },
            body: JSON.stringify(data)
        });

        console.log('Response status:', response.status);
        console.log('Response headers:', Object.fromEntries(response.headers.entries()));

        const result = await response.json();
        console.log('Response data:', result);

        if (!response.ok) {
            throw new Error(result.message || 'Profile update failed');
        }

        return result;
    } catch (error) {
        console.error('Profile update error:', error);
        throw error;
    }
};

// Test function to verify the fix
export const testProfileUpdate = async () => {
    const API_BASE_URL = 'http://localhost:5000/api';
    
    // Get token from localStorage (assuming user is logged in)
    const token = localStorage.getItem('authToken');
    
    if (!token) {
        console.error('No auth token found. Please login first.');
        return;
    }

    const testData = {
        address: 'Test Address from Frontend Fix'
    };

    try {
        const result = await updateProfileWithCorrectHeaders(
            `${API_BASE_URL}/auth/profile`,
            testData,
            token
        );
        
        console.log('✅ Profile update successful:', result);
        return result;
    } catch (error) {
        console.error('❌ Profile update failed:', error);
        throw error;
    }
};

// Alternative axios-based solution
export const updateProfileWithAxios = async (data, token) => {
    const axios = (await import('axios')).default;
    const API_BASE_URL = 'http://localhost:5000/api';

    try {
        const response = await axios.put(`${API_BASE_URL}/auth/profile`, data, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`,
                'Accept': 'application/json'
            }
        });

        return response.data;
    } catch (error) {
        console.error('Axios profile update error:', error);
        throw error;
    }
};

// Export for use in React components
export default {
    updateProfileWithCorrectHeaders,
    testProfileUpdate,
    updateProfileWithAxios
};