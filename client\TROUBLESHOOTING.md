# Troubleshooting Guide

## JSX Syntax Error: "The character '>' is not valid inside a JSX element"

### Problem
You're seeing an error like:
```
[vite] (client) warning: The character ">" is not valid inside a JSX element
237| left: isMobile ? 0 : isTablet && collapsed ? 0 : getSiderWidth()
238| }}>
239| >
240| <Button
```

### Possible Causes
1. **Stray `>` character** - An extra `>` character in JSX
2. **Malformed JSX structure** - Incorrect nesting or closing of JSX elements
3. **Hot reload cache issue** - Vite's hot module replacement cache is corrupted

### Solutions

#### Solution 1: Quick Fix - Clear Vite Cache
```bash
# Stop the development server (Ctrl+C)
# Then run:
npm run fix-deps
# Or manually:
rm -rf node_modules/.vite
npm cache clean --force
npm run dev
```

#### Solution 2: Check JSX Syntax
```bash
npm run check-syntax
```

#### Solution 3: Complete Reset (Windows)
```bash
# Run the batch file:
fix-all-issues.bat
```

#### Solution 4: Manual Steps
1. Stop the development server
2. Delete `node_modules/.vite` folder
3. Clear npm cache: `npm cache clean --force`
4. Restart: `npm run dev`

### Common JSX Syntax Issues to Check

1. **Stray `>` characters**:
   ```jsx
   // ❌ Wrong
   <div style={{
     left: isMobile ? 0 : getSiderWidth()
   }}>
   > // This is the problem!
   <Button />
   
   // ✅ Correct
   <div style={{
     left: isMobile ? 0 : getSiderWidth()
   }}>
     <Button />
   ```

2. **Unclosed JSX tags**:
   ```jsx
   // ❌ Wrong
   <Header
     style={{
       background: colorBgContainer
     }}
   >
   
   // ✅ Correct
   <Header
     style={{
       background: colorBgContainer
     }}
   >
     {/* content */}
   </Header>
   ```

3. **Malformed style objects**:
   ```jsx
   // ❌ Wrong
   <div style={{
     left: isMobile ? 0 : getSiderWidth()
   }}>
   
   // ✅ Correct
   <div style={{
     left: isMobile ? 0 : getSiderWidth()
   }}>
   ```

## Vite Dependency Error: EPERM Operation Not Permitted

### Problem
```
Error: EPERM: operation not permitted, rename 'node_modules\.vite\deps' -> 'node_modules\.vite\deps_temp_*'
```

### Causes
1. File system permissions
2. Antivirus software blocking file operations
3. Multiple Node.js processes running
4. Windows file locking

### Solutions

#### Solution 1: Stop All Node Processes
```bash
# Windows
taskkill /f /im node.exe

# Then restart
npm run dev
```

#### Solution 2: Run as Administrator
- Right-click Command Prompt/PowerShell
- Select "Run as Administrator"
- Navigate to project directory
- Run `npm run dev`

#### Solution 3: Exclude from Antivirus
- Add your project folder to antivirus exclusions
- Add `node_modules` folder to exclusions

#### Solution 4: Complete Clean Install
```bash
# Stop all processes
taskkill /f /im node.exe

# Remove problematic folders
rmdir /s /q node_modules
del package-lock.json

# Fresh install
npm install
npm run dev
```

## Prevention Tips

1. **Always stop the dev server properly** (Ctrl+C)
2. **Don't edit files while build is running**
3. **Use proper JSX syntax validation** in your editor
4. **Regular cache cleaning**: `npm run fix-deps`

## Editor Setup

### VS Code Extensions
- ES7+ React/Redux/React-Native snippets
- Bracket Pair Colorizer
- Auto Rename Tag
- Prettier - Code formatter

### VS Code Settings
```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "emmet.includeLanguages": {
    "javascript": "javascriptreact"
  }
}
```

## If Problems Persist

1. **Check browser console** for additional error details
2. **Try incognito/private browsing** to rule out browser cache
3. **Update Node.js** to latest LTS version
4. **Check file permissions** on project directory
5. **Restart your computer** (Windows file locking issues)

## Getting Help

If none of these solutions work:

1. **Check the exact error message** in the console
2. **Note the specific file and line number**
3. **Try the syntax checker**: `npm run check-syntax`
4. **Look for recent file changes** that might have introduced the error

## Quick Commands Reference

```bash
# Check for syntax errors
npm run check-syntax

# Fix dependency issues
npm run fix-deps

# Complete reset (Windows)
fix-all-issues.bat

# Manual cache clear
rm -rf node_modules/.vite
npm cache clean --force

# Kill all Node processes (Windows)
taskkill /f /im node.exe
```