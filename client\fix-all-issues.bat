@echo off
echo Comprehensive fix for Vite and JSX issues...

echo Step 1: Stopping any running processes...
taskkill /f /im node.exe 2>nul
timeout /t 2 /nobreak >nul

echo Step 2: Cleaning Vite cache and temporary files...
if exist "node_modules\.vite" (
    echo Removing Vite cache...
    rmdir /s /q "node_modules\.vite" 2>nul
)

if exist "node_modules\.vite\deps_temp*" (
    echo Removing temporary Vite dependencies...
    for /d %%i in ("node_modules\.vite\deps_temp*") do rmdir /s /q "%%i" 2>nul
)

echo Step 3: Cleaning npm cache...
npm cache clean --force

echo Step 4: Removing node_modules and package-lock.json...
if exist "node_modules" (
    rmdir /s /q "node_modules"
)
if exist "package-lock.json" (
    del "package-lock.json"
)

echo Step 5: Fresh install of dependencies...
npm install

echo Step 6: Clearing browser cache (optional)...
echo Please clear your browser cache manually if issues persist

echo Step 7: Starting development server...
echo Starting in 3 seconds...
timeout /t 3 /nobreak >nul
npm run dev

echo Done! If issues persist, please check the console for specific error messages.
pause