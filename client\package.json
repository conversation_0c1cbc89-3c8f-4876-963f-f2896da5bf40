{"name": "ecommerce", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:order-flow": "vitest run src/tests/orderStatusFlow.test.js"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.11", "antd": "^5.26.4", "axios": "^1.10.0", "chart.js": "^4.5.0", "dayjs": "^1.11.13", "jwt-decode": "^4.0.0", "libphonenumber-js": "^1.12.10", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-phone-number-input": "^3.4.12", "react-redux": "^9.2.0", "react-router-dom": "^7.6.3", "socket.io-client": "^4.8.1", "tailwindcss": "^4.1.11"}, "devDependencies": {"@eslint/js": "^9.30.1", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jsdom": "^23.0.1", "vite": "^7.0.4", "vitest": "^1.0.4", "@vitest/ui": "^1.0.4"}}